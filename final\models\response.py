"""
Response-related models for the Final RAG Model
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum
from .query import Query, QueryResult
from .document import DocumentChunk


class ResponseQuality(str, Enum):
    """Response quality levels"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"


class EmbeddingResult(BaseModel):
    """Result from embedding generation"""
    text: str = Field(..., description="Original text")
    embedding: List[float] = Field(..., description="Generated embedding vector")
    model_name: str = Field(..., description="Embedding model used")
    dimension: int = Field(..., description="Embedding dimension")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")
    
    @validator('embedding')
    def validate_embedding(cls, v):
        """Validate embedding vector"""
        if not v:
            raise ValueError("Embedding cannot be empty")
        if not all(isinstance(x, (int, float)) for x in v):
            raise ValueError("Embedding must contain only numeric values")
        return v


class ChunkProcessingResult(BaseModel):
    """Result from processing a single chunk"""
    chunk_id: str = Field(..., description="Chunk identifier")
    processed: bool = Field(..., description="Whether chunk was processed")
    processing_time_ms: float = Field(..., description="Processing time")
    
    # Quality metrics
    quality_score: float = Field(..., description="Quality score", ge=0.0, le=1.0)
    relevance_score: Optional[float] = Field(None, description="Relevance score")
    
    # Processing details
    embedding_generated: bool = Field(default=False, description="Whether embedding was generated")
    keywords_extracted: bool = Field(default=False, description="Whether keywords were extracted")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")


class GenerationMetrics(BaseModel):
    """Metrics for answer generation"""
    prompt_tokens: int = Field(..., description="Number of tokens in prompt")
    completion_tokens: int = Field(..., description="Number of tokens in completion")
    total_tokens: int = Field(..., description="Total tokens used")
    
    # Timing metrics
    prompt_processing_time_ms: float = Field(..., description="Time to process prompt")
    generation_time_ms: float = Field(..., description="Time to generate response")
    total_time_ms: float = Field(..., description="Total generation time")
    
    # Quality metrics
    response_length: int = Field(..., description="Response length in characters")
    coherence_score: Optional[float] = Field(None, description="Coherence score")
    relevance_score: Optional[float] = Field(None, description="Relevance score")


class AnswerGeneration(BaseModel):
    """Detailed answer generation information"""
    query_id: str = Field(..., description="Query identifier")
    answer_text: str = Field(..., description="Generated answer")
    
    # Generation context
    context_chunks: List[str] = Field(..., description="Chunk IDs used for context")
    context_length: int = Field(..., description="Total context length")
    model_used: str = Field(..., description="Generation model used")
    
    # Generation parameters
    temperature: float = Field(..., description="Generation temperature")
    max_tokens: int = Field(..., description="Maximum tokens for generation")
    
    # Metrics
    metrics: GenerationMetrics = Field(..., description="Generation metrics")
    
    # Quality assessment
    quality: Optional[ResponseQuality] = Field(None, description="Assessed response quality")
    confidence_score: float = Field(..., description="Confidence in the answer", ge=0.0, le=1.0)
    
    @validator('answer_text')
    def validate_answer(cls, v):
        """Validate answer text"""
        if not v or not v.strip():
            raise ValueError("Answer text cannot be empty")
        return v.strip()


class ProcessingStatistics(BaseModel):
    """Comprehensive processing statistics"""
    # Document processing
    documents_processed: int = Field(default=0, description="Number of documents processed")
    total_chunks_created: int = Field(default=0, description="Total chunks created")
    chunks_processed: int = Field(default=0, description="Chunks actually processed")
    chunks_skipped: int = Field(default=0, description="Chunks skipped due to optimization")
    
    # Embedding statistics
    embeddings_generated: int = Field(default=0, description="New embeddings generated")
    embeddings_cached: int = Field(default=0, description="Embeddings retrieved from cache")
    embedding_cache_hit_rate: float = Field(default=0.0, description="Cache hit rate")
    
    # Search statistics
    total_searches: int = Field(default=0, description="Total search operations")
    average_search_time_ms: float = Field(default=0.0, description="Average search time")
    chunks_above_threshold: int = Field(default=0, description="Chunks above similarity threshold")
    
    # Generation statistics
    total_generations: int = Field(default=0, description="Total answer generations")
    average_generation_time_ms: float = Field(default=0.0, description="Average generation time")
    total_tokens_used: int = Field(default=0, description="Total tokens consumed")
    
    # Performance metrics
    total_processing_time_ms: float = Field(default=0.0, description="Total processing time")
    memory_usage_mb: Optional[float] = Field(None, description="Peak memory usage")
    cpu_usage_percent: Optional[float] = Field(None, description="Average CPU usage")
    
    @property
    def processing_efficiency(self) -> float:
        """Calculate processing efficiency (chunks processed / total chunks)"""
        if self.total_chunks_created == 0:
            return 0.0
        return self.chunks_processed / self.total_chunks_created
    
    @property
    def average_chunks_per_query(self) -> float:
        """Calculate average chunks processed per query"""
        if self.total_searches == 0:
            return 0.0
        return self.chunks_processed / self.total_searches


class OptimizationReport(BaseModel):
    """Report on optimization effectiveness"""
    # Chunk selection optimization
    total_chunks_available: int = Field(..., description="Total chunks available")
    chunks_selected: int = Field(..., description="Chunks selected for processing")
    selection_ratio: float = Field(..., description="Selection ratio (selected/total)")
    
    # Time savings
    estimated_full_processing_time_ms: float = Field(..., description="Estimated time for full processing")
    actual_processing_time_ms: float = Field(..., description="Actual processing time")
    time_saved_ms: float = Field(..., description="Time saved through optimization")
    time_savings_percentage: float = Field(..., description="Percentage of time saved")
    
    # Quality impact
    quality_maintained: bool = Field(..., description="Whether quality was maintained")
    quality_score_difference: float = Field(..., description="Quality score difference vs full processing")
    
    # Resource savings
    tokens_saved: int = Field(default=0, description="Tokens saved through optimization")
    memory_saved_mb: float = Field(default=0.0, description="Memory saved")
    
    @property
    def efficiency_gain(self) -> float:
        """Calculate efficiency gain from optimization"""
        if self.estimated_full_processing_time_ms == 0:
            return 0.0
        return (self.time_saved_ms / self.estimated_full_processing_time_ms) * 100


class RAGProcessingResult(BaseModel):
    """Complete result from RAG processing"""
    request_id: str = Field(..., description="Request identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
    
    # Query and results
    query_results: List[QueryResult] = Field(..., description="Query processing results")
    answer_generations: List[AnswerGeneration] = Field(..., description="Answer generation results")
    
    # Processing information
    statistics: ProcessingStatistics = Field(..., description="Processing statistics")
    optimization_report: OptimizationReport = Field(..., description="Optimization effectiveness")
    
    # Status and errors
    success: bool = Field(..., description="Whether processing was successful")
    error_messages: List[str] = Field(default_factory=list, description="Any error messages")
    warnings: List[str] = Field(default_factory=list, description="Any warnings")
    
    # Performance metrics
    total_processing_time_ms: float = Field(..., description="Total processing time")
    peak_memory_usage_mb: Optional[float] = Field(None, description="Peak memory usage")
    
    def get_successful_answers(self) -> List[AnswerGeneration]:
        """Get successfully generated answers"""
        return [ag for ag in self.answer_generations if ag.answer_text.strip()]
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get a summary of processing results"""
        return {
            "total_queries": len(self.query_results),
            "successful_answers": len(self.get_successful_answers()),
            "total_chunks_processed": self.statistics.chunks_processed,
            "processing_time_ms": self.total_processing_time_ms,
            "efficiency_gain": self.optimization_report.efficiency_gain,
            "quality_maintained": self.optimization_report.quality_maintained,
            "success": self.success
        }
