#!/usr/bin/env python3
"""
Final RAG Model - Startup Script
Optimized RAG system with efficient chunk processing
"""
import asyncio
import sys
import os
from pathlib import Path
import uvicorn
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint

# Add the final directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import get_settings, validate_environment, get_config_summary
from utils.logger import setup_logging, get_logger

# Initialize
console = Console()
app = typer.Typer(help="Final RAG Model - Optimized RAG system")
settings = get_settings()


@app.command()
def start(
    host: str = typer.Option(None, "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(None, "--port", "-p", help="Port to bind to"),
    reload: bool = typer.Option(False, "--reload", "-r", help="Enable auto-reload"),
    log_level: str = typer.Option(None, "--log-level", "-l", help="Log level"),
    workers: int = typer.Option(1, "--workers", "-w", help="Number of workers")
):
    """Start the Final RAG Model API server"""
    
    # Setup logging
    setup_logging(log_level=log_level or settings.log_level)
    logger = get_logger(__name__)
    
    try:
        # Validate environment
        validate_environment()
        
        # Use provided values or defaults from settings
        host = host or settings.api_host
        port = port or settings.api_port
        
        # Display startup information
        display_startup_info(host, port)
        
        # Start server
        logger.info(f"Starting Final RAG Model API on {host}:{port}")
        
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level.lower() if log_level else settings.log_level.lower(),
            workers=workers if not reload else 1,
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
        console.print(f"[red]Error: {str(e)}[/red]")
        raise typer.Exit(1)


@app.command()
def config():
    """Display current configuration"""
    try:
        config_summary = get_config_summary()
        
        # Create configuration table
        table = Table(title="Final RAG Model Configuration")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in config_summary.items():
            table.add_row(key.replace('_', ' ').title(), str(value))
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error displaying configuration: {str(e)}[/red]")
        raise typer.Exit(1)


@app.command()
def test():
    """Run basic system tests"""
    try:
        console.print("[yellow]Running system tests...[/yellow]")
        
        # Test environment
        console.print("✓ Testing environment variables...")
        validate_environment()
        
        # Test imports
        console.print("✓ Testing imports...")
        from services.rag_engine import RAGEngine
        from services.embedding_service import OptimizedEmbeddingService
        from services.vector_store import OptimizedVectorStore
        
        console.print("✓ Testing configuration...")
        config_summary = get_config_summary()
        
        # Test basic functionality
        console.print("✓ Testing basic functionality...")
        
        console.print("[green]All tests passed! ✓[/green]")
        
        # Display test results
        results_table = Table(title="Test Results")
        results_table.add_column("Component", style="cyan")
        results_table.add_column("Status", style="green")
        
        results_table.add_row("Environment", "✓ PASS")
        results_table.add_row("Imports", "✓ PASS")
        results_table.add_row("Configuration", "✓ PASS")
        results_table.add_row("Basic Functionality", "✓ PASS")
        
        console.print(results_table)
        
    except Exception as e:
        console.print(f"[red]Test failed: {str(e)}[/red]")
        raise typer.Exit(1)


@app.command()
def demo():
    """Run a demo query"""
    try:
        console.print("[yellow]Running demo query...[/yellow]")
        
        # This would require the server to be running
        console.print("[blue]Demo functionality requires the server to be running.[/blue]")
        console.print("[blue]Start the server with 'python run.py start' and then use the API endpoints.[/blue]")
        
        # Display example usage
        display_demo_info()
        
    except Exception as e:
        console.print(f"[red]Demo failed: {str(e)}[/red]")
        raise typer.Exit(1)


@app.command()
def info():
    """Display system information"""
    try:
        # System info
        info_table = Table(title="Final RAG Model - System Information")
        info_table.add_column("Component", style="cyan")
        info_table.add_column("Details", style="green")
        
        info_table.add_row("Version", "1.0.0")
        info_table.add_row("Python Version", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        info_table.add_row("Platform", sys.platform)
        info_table.add_row("Working Directory", str(Path.cwd()))
        
        # Configuration info
        config_summary = get_config_summary()
        info_table.add_row("Model", config_summary.get("model", "N/A"))
        info_table.add_row("Chunk Size", str(config_summary.get("chunk_size", "N/A")))
        info_table.add_row("Max Chunks", str(config_summary.get("max_chunks", "N/A")))
        info_table.add_row("Smart Chunking", str(config_summary.get("smart_chunking", "N/A")))
        
        console.print(info_table)
        
        # Features
        features_panel = Panel(
            """
• Smart chunk selection for optimal performance
• Optimized embedding caching
• Batch processing capabilities
• Multiple selection strategies (semantic, keyword, hybrid, smart)
• Real-time performance monitoring
• Comprehensive error handling and validation
• RESTful API with automatic documentation
            """.strip(),
            title="Key Features",
            border_style="blue"
        )
        console.print(features_panel)
        
    except Exception as e:
        console.print(f"[red]Error displaying info: {str(e)}[/red]")
        raise typer.Exit(1)


def display_startup_info(host: str, port: int):
    """Display startup information"""
    startup_panel = Panel(
        f"""
[bold green]Final RAG Model API[/bold green]
[blue]Optimized RAG system with efficient chunk processing[/blue]

🚀 Server: http://{host}:{port}
📚 Docs: http://{host}:{port}/docs
🔍 Health: http://{host}:{port}/health
📊 Stats: http://{host}:{port}/stats

[yellow]Press Ctrl+C to stop the server[/yellow]
        """.strip(),
        title="Starting Server",
        border_style="green"
    )
    console.print(startup_panel)


def display_demo_info():
    """Display demo information"""
    demo_panel = Panel(
        """
[bold]Example API Usage:[/bold]

1. Single Query:
   POST /query/single
   {
     "query": "What is the main topic?",
     "document_url": "https://example.com/doc.pdf"
   }

2. Full RAG Request:
   POST /query
   {
     "documents": "https://example.com/doc.pdf",
     "questions": ["What is the main topic?", "What are the key points?"],
     "chunk_strategy": "smart",
     "max_chunks": 5
   }

3. System Stats:
   GET /stats

4. Health Check:
   GET /health
        """.strip(),
        title="Demo Usage",
        border_style="blue"
    )
    console.print(demo_panel)


if __name__ == "__main__":
    app()
