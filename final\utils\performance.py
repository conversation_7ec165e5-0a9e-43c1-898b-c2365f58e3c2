"""
Performance monitoring utilities for Final RAG Model
"""
import time
import psutil
import asyncio
from typing import Dict, Any, Optional, Callable
from contextlib import contextmanager, asynccontextmanager
from functools import wraps
from dataclasses import dataclass, field
from collections import defaultdict

from .logger import get_logger, get_performance_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    operation: str
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    memory_start_mb: Optional[float] = None
    memory_end_mb: Optional[float] = None
    memory_peak_mb: Optional[float] = None
    cpu_percent: Optional[float] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)
    
    def finalize(self):
        """Finalize metrics calculation"""
        if self.end_time is None:
            self.end_time = time.time()
        
        if self.duration_ms is None:
            self.duration_ms = (self.end_time - self.start_time) * 1000
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "operation": self.operation,
            "duration_ms": self.duration_ms,
            "memory_peak_mb": self.memory_peak_mb,
            "cpu_percent": self.cpu_percent,
            **self.additional_data
        }


class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self):
        self.metrics_history = defaultdict(list)
        self.active_operations = {}
        
    def start_operation(self, operation: str, **kwargs) -> str:
        """Start monitoring an operation"""
        operation_id = f"{operation}_{time.time()}"
        
        metrics = PerformanceMetrics(
            operation=operation,
            start_time=time.time(),
            memory_start_mb=self._get_memory_usage(),
            additional_data=kwargs
        )
        
        self.active_operations[operation_id] = metrics
        return operation_id
    
    def end_operation(self, operation_id: str, **kwargs) -> PerformanceMetrics:
        """End monitoring an operation"""
        if operation_id not in self.active_operations:
            logger.warning(f"Operation {operation_id} not found in active operations")
            return None
        
        metrics = self.active_operations.pop(operation_id)
        metrics.end_time = time.time()
        metrics.memory_end_mb = self._get_memory_usage()
        metrics.cpu_percent = psutil.cpu_percent()
        metrics.additional_data.update(kwargs)
        metrics.finalize()
        
        # Store in history
        self.metrics_history[metrics.operation].append(metrics)
        
        # Log performance
        perf_logger.log_timing(
            metrics.operation,
            metrics.duration_ms,
            **metrics.additional_data
        )
        
        if metrics.memory_peak_mb:
            perf_logger.log_memory(
                metrics.operation,
                metrics.memory_peak_mb,
                **metrics.additional_data
            )
        
        return metrics
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def get_operation_stats(self, operation: str) -> Dict[str, Any]:
        """Get statistics for a specific operation"""
        if operation not in self.metrics_history:
            return {}
        
        metrics_list = self.metrics_history[operation]
        durations = [m.duration_ms for m in metrics_list if m.duration_ms]
        
        if not durations:
            return {}
        
        return {
            "operation": operation,
            "count": len(durations),
            "average_duration_ms": sum(durations) / len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "total_duration_ms": sum(durations),
            "last_execution": metrics_list[-1].to_dict() if metrics_list else None
        }
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all operations"""
        return {
            operation: self.get_operation_stats(operation)
            for operation in self.metrics_history.keys()
        }
    
    def clear_history(self):
        """Clear metrics history"""
        self.metrics_history.clear()
        logger.info("Performance metrics history cleared")


# Global performance monitor
_performance_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor"""
    return _performance_monitor


@contextmanager
def measure_time(operation: str, **kwargs):
    """Context manager to measure execution time"""
    monitor = get_performance_monitor()
    operation_id = monitor.start_operation(operation, **kwargs)
    
    try:
        yield
    finally:
        metrics = monitor.end_operation(operation_id)
        if metrics:
            logger.debug(f"Operation '{operation}' completed in {metrics.duration_ms:.2f}ms")


@asynccontextmanager
async def measure_async_time(operation: str, **kwargs):
    """Async context manager to measure execution time"""
    monitor = get_performance_monitor()
    operation_id = monitor.start_operation(operation, **kwargs)
    
    try:
        yield
    finally:
        metrics = monitor.end_operation(operation_id)
        if metrics:
            logger.debug(f"Async operation '{operation}' completed in {metrics.duration_ms:.2f}ms")


def timed(operation: str = None):
    """Decorator to measure function execution time"""
    def decorator(func: Callable):
        op_name = operation or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            with measure_time(op_name):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def async_timed(operation: str = None):
    """Decorator to measure async function execution time"""
    def decorator(func: Callable):
        op_name = operation or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async with measure_async_time(op_name):
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator


class ResourceMonitor:
    """Monitor system resources"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_task = None
        self.resource_history = []
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start resource monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("Resource monitoring started")
    
    async def stop_monitoring(self):
        """Stop resource monitoring"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Resource monitoring stopped")
    
    async def _monitor_loop(self, interval: float):
        """Resource monitoring loop"""
        while self.monitoring:
            try:
                # Get system metrics
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                # Get process metrics
                process = psutil.Process()
                process_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                resource_data = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / 1024 / 1024 / 1024,
                    "disk_percent": disk.percent,
                    "process_memory_mb": process_memory
                }
                
                self.resource_history.append(resource_data)
                
                # Keep only last 1000 entries
                if len(self.resource_history) > 1000:
                    self.resource_history = self.resource_history[-1000:]
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in resource monitoring: {str(e)}")
                await asyncio.sleep(interval)
    
    def get_current_resources(self) -> Dict[str, Any]:
        """Get current resource usage"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / 1024 / 1024 / 1024,
                "disk_percent": disk.percent,
                "process_memory_mb": process_memory,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Error getting current resources: {str(e)}")
            return {}
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get resource usage summary"""
        if not self.resource_history:
            return self.get_current_resources()
        
        # Calculate averages from history
        cpu_values = [r["cpu_percent"] for r in self.resource_history]
        memory_values = [r["memory_percent"] for r in self.resource_history]
        process_memory_values = [r["process_memory_mb"] for r in self.resource_history]
        
        return {
            "monitoring_active": self.monitoring,
            "history_entries": len(self.resource_history),
            "cpu_average": sum(cpu_values) / len(cpu_values),
            "cpu_max": max(cpu_values),
            "memory_average": sum(memory_values) / len(memory_values),
            "memory_max": max(memory_values),
            "process_memory_average": sum(process_memory_values) / len(process_memory_values),
            "process_memory_max": max(process_memory_values),
            "current": self.get_current_resources()
        }


# Global resource monitor
_resource_monitor = ResourceMonitor()


def get_resource_monitor() -> ResourceMonitor:
    """Get the global resource monitor"""
    return _resource_monitor
