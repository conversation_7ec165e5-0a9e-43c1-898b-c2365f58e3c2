"""
Text processing utilities for Final RAG Model
"""
import re
import string
from typing import List, Dict, Set, Optional, Tuple
from collections import Counter
import unicodedata

from .logger import get_logger

logger = get_logger(__name__)


class TextProcessor:
    """Advanced text processing utilities"""
    
    def __init__(self):
        # Common stop words
        self.stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'would', 'you', 'your', 'have', 'had',
            'this', 'these', 'they', 'them', 'their', 'there', 'where', 'when',
            'what', 'which', 'who', 'why', 'how', 'can', 'could', 'should',
            'would', 'may', 'might', 'must', 'shall', 'will', 'do', 'does',
            'did', 'done', 'am', 'is', 'are', 'was', 'were', 'been', 'being'
        }
        
        # Punctuation to remove
        self.punctuation = set(string.punctuation)
        
        # Patterns for cleaning
        self.url_pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}')
        self.whitespace_pattern = re.compile(r'\s+')
        
    def clean_text(self, text: str, 
                   remove_urls: bool = True,
                   remove_emails: bool = True,
                   remove_phones: bool = True,
                   normalize_whitespace: bool = True,
                   remove_extra_punctuation: bool = True) -> str:
        """
        Clean and normalize text
        
        Args:
            text: Input text
            remove_urls: Remove URLs
            remove_emails: Remove email addresses
            remove_phones: Remove phone numbers
            normalize_whitespace: Normalize whitespace
            remove_extra_punctuation: Remove excessive punctuation
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Normalize unicode
        text = unicodedata.normalize('NFKD', text)
        
        # Remove URLs
        if remove_urls:
            text = self.url_pattern.sub(' ', text)
        
        # Remove emails
        if remove_emails:
            text = self.email_pattern.sub(' ', text)
        
        # Remove phone numbers
        if remove_phones:
            text = self.phone_pattern.sub(' ', text)
        
        # Remove extra punctuation (multiple consecutive punctuation marks)
        if remove_extra_punctuation:
            text = re.sub(r'[^\w\s]{2,}', ' ', text)
        
        # Normalize whitespace
        if normalize_whitespace:
            text = self.whitespace_pattern.sub(' ', text)
        
        return text.strip()
    
    def extract_keywords(self, text: str, 
                        min_length: int = 3,
                        max_keywords: int = 20,
                        include_phrases: bool = True) -> List[str]:
        """
        Extract keywords from text
        
        Args:
            text: Input text
            min_length: Minimum keyword length
            max_keywords: Maximum number of keywords
            include_phrases: Include 2-word phrases
            
        Returns:
            List of keywords
        """
        if not text:
            return []
        
        # Clean text
        cleaned_text = self.clean_text(text).lower()
        
        # Extract single words
        words = re.findall(r'\b[a-zA-Z]{' + str(min_length) + ',}\b', cleaned_text)
        
        # Filter stop words
        words = [word for word in words if word not in self.stop_words]
        
        # Count word frequencies
        word_counts = Counter(words)
        
        # Get top single words
        keywords = [word for word, count in word_counts.most_common(max_keywords)]
        
        # Extract phrases if requested
        if include_phrases:
            phrases = self._extract_phrases(cleaned_text, min_length)
            # Add top phrases
            phrase_counts = Counter(phrases)
            top_phrases = [phrase for phrase, count in phrase_counts.most_common(max_keywords // 2)]
            keywords.extend(top_phrases)
        
        return keywords[:max_keywords]
    
    def _extract_phrases(self, text: str, min_length: int) -> List[str]:
        """Extract 2-word phrases from text"""
        words = re.findall(r'\b[a-zA-Z]{' + str(min_length) + ',}\b', text)
        words = [word for word in words if word not in self.stop_words]
        
        phrases = []
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i + 1]}"
            phrases.append(phrase)
        
        return phrases
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts using word overlap
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # Extract keywords from both texts
        keywords1 = set(self.extract_keywords(text1, include_phrases=False))
        keywords2 = set(self.extract_keywords(text2, include_phrases=False))
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        return intersection / union if union > 0 else 0.0
    
    def extract_sentences(self, text: str, min_length: int = 10) -> List[str]:
        """
        Extract sentences from text
        
        Args:
            text: Input text
            min_length: Minimum sentence length
            
        Returns:
            List of sentences
        """
        if not text:
            return []
        
        # Split on sentence boundaries
        sentences = re.split(r'[.!?]+', text)
        
        # Clean and filter sentences
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) >= min_length:
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def calculate_readability_score(self, text: str) -> Dict[str, float]:
        """
        Calculate readability metrics for text
        
        Args:
            text: Input text
            
        Returns:
            Dictionary with readability metrics
        """
        if not text:
            return {"words": 0, "sentences": 0, "avg_words_per_sentence": 0}
        
        # Count words and sentences
        words = len(re.findall(r'\b\w+\b', text))
        sentences = len(re.findall(r'[.!?]+', text))
        
        if sentences == 0:
            sentences = 1  # Avoid division by zero
        
        avg_words_per_sentence = words / sentences
        
        # Calculate complexity score (simplified)
        complexity_score = min(avg_words_per_sentence / 20, 1.0)  # Normalize to 0-1
        
        return {
            "words": words,
            "sentences": sentences,
            "avg_words_per_sentence": avg_words_per_sentence,
            "complexity_score": complexity_score
        }
    
    def detect_language(self, text: str) -> str:
        """
        Simple language detection (basic implementation)
        
        Args:
            text: Input text
            
        Returns:
            Detected language code
        """
        if not text:
            return "unknown"
        
        # Very basic language detection based on common words
        text_lower = text.lower()
        
        # English indicators
        english_words = ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with']
        english_count = sum(1 for word in english_words if word in text_lower)
        
        # Spanish indicators
        spanish_words = ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no']
        spanish_count = sum(1 for word in spanish_words if word in text_lower)
        
        # French indicators
        french_words = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir']
        french_count = sum(1 for word in french_words if word in text_lower)
        
        # Determine language based on highest count
        counts = {'en': english_count, 'es': spanish_count, 'fr': french_count}
        detected_lang = max(counts, key=counts.get)
        
        # Return 'en' if no clear winner or if English has highest count
        return detected_lang if counts[detected_lang] > 0 else 'en'


# Global text processor instance
_text_processor = TextProcessor()


def clean_text(text: str, **kwargs) -> str:
    """Clean text using global processor"""
    return _text_processor.clean_text(text, **kwargs)


def extract_keywords(text: str, **kwargs) -> List[str]:
    """Extract keywords using global processor"""
    return _text_processor.extract_keywords(text, **kwargs)


def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity using global processor"""
    return _text_processor.calculate_text_similarity(text1, text2)


def extract_sentences(text: str, **kwargs) -> List[str]:
    """Extract sentences using global processor"""
    return _text_processor.extract_sentences(text, **kwargs)


def get_readability_score(text: str) -> Dict[str, float]:
    """Get readability score using global processor"""
    return _text_processor.calculate_readability_score(text)


def detect_language(text: str) -> str:
    """Detect language using global processor"""
    return _text_processor.detect_language(text)


def get_text_processor() -> TextProcessor:
    """Get the global text processor instance"""
    return _text_processor
