"""
Vector Store Service for Final RAG Model
Optimized FAISS-based vector storage with efficient search
"""
import asyncio
import numpy as np
import faiss
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import pickle
import time

# Internal imports
from ..models.document import DocumentChunk
from ..models.query import SearchResult, Query
from ..services.embedding_service import OptimizedEmbeddingService
from ..config import get_settings
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class VectorStore:
    """Base vector store with FAISS backend"""
    
    def __init__(self, embedding_service: OptimizedEmbeddingService = None):
        self.embedding_service = embedding_service or OptimizedEmbeddingService()
        self.index = None
        self.chunks = []
        self.dimension = settings.vector_dimension
        self.index_path = Path(settings.faiss_index_path)
        
    async def initialize(self):
        """Initialize the vector store"""
        try:
            logger.info("Initializing vector store...")
            
            # Initialize embedding service
            await self.embedding_service.initialize()
            self.dimension = self.embedding_service.get_dimension()
            
            # Create FAISS index
            self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
            
            # Try to load existing index
            await self._load_index()
            
            logger.info(f"Vector store initialized with dimension {self.dimension}")
            
        except Exception as e:
            logger.error(f"Error initializing vector store: {str(e)}")
            raise
    
    async def add_chunks(self, chunks: List[DocumentChunk]) -> None:
        """
        Add document chunks to the vector store
        
        Args:
            chunks: List of chunks to add
        """
        try:
            if not chunks:
                logger.warning("No chunks provided to add")
                return
            
            logger.info(f"Adding {len(chunks)} chunks to vector store")
            start_time = time.time()
            
            # Clear existing data for new document
            self.chunks = []
            self.index.reset()
            
            # Generate embeddings for chunks
            chunk_texts = [chunk.content for chunk in chunks]
            embeddings = await self.embedding_service.encode_batch(
                chunk_texts, 
                task_type="retrieval_document"
            )
            
            # Add embeddings to chunks
            for i, chunk in enumerate(chunks):
                chunk.embedding = embeddings[i].tolist()
                chunk.is_processed = True
            
            # Add to FAISS index
            self.index.add(embeddings.astype(np.float32))
            
            # Store chunks
            self.chunks = chunks
            
            # Save index
            await self._save_index()
            
            processing_time = (time.time() - start_time) * 1000
            logger.info(f"Added {len(chunks)} chunks in {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"Error adding chunks: {str(e)}")
            raise
    
    async def search(self, query: str, top_k: int = 5, 
                    similarity_threshold: float = None) -> List[SearchResult]:
        """
        Search for similar chunks
        
        Args:
            query: Search query
            top_k: Number of results to return
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            List of search results
        """
        try:
            if not self.chunks:
                logger.warning("No chunks in vector store")
                return []
            
            similarity_threshold = similarity_threshold or settings.similarity_threshold
            
            logger.debug(f"Searching for: '{query[:50]}...' with top_k={top_k}")
            start_time = time.time()
            
            # Generate query embedding
            query_embedding = await self.embedding_service.encode_query(query)
            
            # Search in FAISS index
            scores, indices = self.index.search(
                query_embedding.astype(np.float32),
                min(top_k * 2, len(self.chunks))  # Get more results for filtering
            )
            
            # Create search results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(self.chunks) and score >= similarity_threshold:
                    result = SearchResult(
                        chunk=self.chunks[idx],
                        score=float(score),
                        rank=i + 1,
                        semantic_score=float(score)
                    )
                    results.append(result)
            
            # Limit to requested number
            results = results[:top_k]
            
            search_time = (time.time() - start_time) * 1000
            logger.debug(f"Found {len(results)} results in {search_time:.2f}ms")
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            return []
    
    async def get_relevant_context(self, query: str, max_chunks: int = 3) -> str:
        """
        Get relevant context for query
        
        Args:
            query: Search query
            max_chunks: Maximum number of chunks to include
            
        Returns:
            Concatenated context text
        """
        try:
            search_results = await self.search(query, top_k=max_chunks)
            
            if not search_results:
                return ""
            
            # Concatenate chunk content
            context_parts = []
            for result in search_results:
                context_parts.append(result.chunk.content)
            
            context = "\n\n".join(context_parts)
            
            # Limit context length
            if len(context) > settings.max_context_length:
                context = context[:settings.max_context_length] + "..."
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting relevant context: {str(e)}")
            return ""
    
    async def _save_index(self):
        """Save FAISS index and chunks to disk"""
        try:
            self.index_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_path.with_suffix('.faiss')))
            
            # Save chunks
            with open(self.index_path.with_suffix('.chunks'), 'wb') as f:
                pickle.dump(self.chunks, f)
            
            logger.debug(f"Saved index with {len(self.chunks)} chunks")
            
        except Exception as e:
            logger.warning(f"Could not save index: {str(e)}")
    
    async def _load_index(self):
        """Load FAISS index and chunks from disk"""
        try:
            faiss_file = self.index_path.with_suffix('.faiss')
            chunks_file = self.index_path.with_suffix('.chunks')
            
            if faiss_file.exists() and chunks_file.exists():
                # Load FAISS index
                self.index = faiss.read_index(str(faiss_file))
                
                # Load chunks
                with open(chunks_file, 'rb') as f:
                    self.chunks = pickle.load(f)
                
                logger.info(f"Loaded index with {len(self.chunks)} chunks")
            
        except Exception as e:
            logger.warning(f"Could not load existing index: {str(e)}")
            # Create new index
            self.index = faiss.IndexFlatIP(self.dimension)
            self.chunks = []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics"""
        return {
            "total_chunks": len(self.chunks),
            "index_size": self.index.ntotal if self.index else 0,
            "dimension": self.dimension,
            "index_type": type(self.index).__name__ if self.index else None
        }


class OptimizedVectorStore(VectorStore):
    """Optimized vector store with advanced search capabilities"""
    
    def __init__(self, embedding_service: OptimizedEmbeddingService = None):
        super().__init__(embedding_service)
        self.search_cache = {}
        self.cache_enabled = True
        
    async def search_with_reranking(self, query: str, top_k: int = 5,
                                  rerank_factor: int = 2) -> List[SearchResult]:
        """
        Search with reranking for better results
        
        Args:
            query: Search query
            top_k: Final number of results
            rerank_factor: Factor to multiply top_k for initial search
            
        Returns:
            Reranked search results
        """
        try:
            # Get more results initially
            initial_k = min(top_k * rerank_factor, len(self.chunks))
            initial_results = await self.search(query, top_k=initial_k)
            
            if len(initial_results) <= top_k:
                return initial_results
            
            # Rerank using additional signals
            reranked_results = await self._rerank_results(query, initial_results)
            
            return reranked_results[:top_k]
            
        except Exception as e:
            logger.error(f"Error in search with reranking: {str(e)}")
            return await self.search(query, top_k)
    
    async def _rerank_results(self, query: str, 
                            results: List[SearchResult]) -> List[SearchResult]:
        """Rerank search results using additional signals"""
        try:
            # Extract query keywords for keyword matching
            query_words = set(query.lower().split())
            
            # Calculate additional scores
            for result in results:
                chunk_text = result.chunk.content.lower()
                chunk_words = set(chunk_text.split())
                
                # Keyword overlap score
                keyword_overlap = len(query_words.intersection(chunk_words)) / len(query_words)
                result.keyword_score = keyword_overlap
                
                # Quality score from chunk metadata
                quality_score = result.chunk.metadata.quality_score or 0.5
                
                # Combined final score
                result.final_score = (
                    result.semantic_score * 0.6 +
                    keyword_overlap * 0.3 +
                    quality_score * 0.1
                )
            
            # Sort by final score
            results.sort(key=lambda x: x.final_score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error reranking results: {str(e)}")
            return results
    
    async def search_with_cache(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """Search with caching for repeated queries"""
        if not self.cache_enabled:
            return await self.search(query, top_k)
        
        # Create cache key
        cache_key = f"{query}_{top_k}_{len(self.chunks)}"
        
        # Check cache
        if cache_key in self.search_cache:
            logger.debug(f"Cache hit for query: {query[:30]}...")
            return self.search_cache[cache_key]
        
        # Perform search
        results = await self.search_with_reranking(query, top_k)
        
        # Cache results (limit cache size)
        if len(self.search_cache) > 100:
            # Remove oldest entries
            oldest_key = next(iter(self.search_cache))
            del self.search_cache[oldest_key]
        
        self.search_cache[cache_key] = results
        
        return results
    
    async def batch_search(self, queries: List[str], 
                          top_k: int = 5) -> List[List[SearchResult]]:
        """
        Perform batch search for multiple queries
        
        Args:
            queries: List of search queries
            top_k: Number of results per query
            
        Returns:
            List of search results for each query
        """
        try:
            logger.info(f"Performing batch search for {len(queries)} queries")
            start_time = time.time()
            
            # Process queries concurrently
            tasks = [
                self.search_with_cache(query, top_k) 
                for query in queries
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            final_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error in batch search for query {i}: {str(result)}")
                    final_results.append([])
                else:
                    final_results.append(result)
            
            processing_time = (time.time() - start_time) * 1000
            logger.info(f"Batch search completed in {processing_time:.2f}ms")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Error in batch search: {str(e)}")
            return [[] for _ in queries]
    
    def clear_cache(self):
        """Clear search cache"""
        self.search_cache.clear()
        logger.info("Search cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cache_entries": len(self.search_cache),
            "cache_enabled": self.cache_enabled
        }
