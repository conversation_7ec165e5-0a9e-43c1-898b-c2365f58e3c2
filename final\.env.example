# Final RAG Model Environment Configuration

# =============================================================================
# GEMINI AI CONFIGURATION
# =============================================================================
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash
GEMINI_EMBEDDING_MODEL=models/embedding-001
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_TOKENS=2048

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_TITLE=Final RAG Model API
API_VERSION=1.0.0

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================
VECTOR_DIMENSION=768
FAISS_INDEX_PATH=./final/data/faiss_index

# =============================================================================
# OPTIMIZED CHUNK PROCESSING
# =============================================================================
CHUNK_SIZE=512
CHUNK_OVERLAP=64
MAX_CHUNKS_PER_QUERY=5
SIMILARITY_THRESHOLD=0.7

# Smart Chunk Selection
ENABLE_SMART_CHUNKING=true
CHUNK_RELEVANCE_THRESHOLD=0.6
MAX_CONTEXT_LENGTH=4000
ENABLE_CHUNK_RERANKING=true

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
BATCH_SIZE=32
MAX_CONCURRENT_REQUESTS=10
CACHE_EMBEDDINGS=true
ENABLE_ASYNC_PROCESSING=true

# =============================================================================
# DOCUMENT PROCESSING
# =============================================================================
SUPPORTED_FORMATS=["pdf", "docx", "txt"]
MAX_FILE_SIZE_MB=50
EXTRACT_METADATA=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=./final/logs/rag_model.log
ENABLE_DETAILED_LOGGING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ENABLE_CORS=true
ALLOWED_ORIGINS=["*"]
API_KEY_HEADER=X-API-Key

# =============================================================================
# RESPONSE CONFIGURATION
# =============================================================================
INCLUDE_SOURCES=true
INCLUDE_CONFIDENCE=true
INCLUDE_REASONING=true
MAX_RESPONSE_LENGTH=2000
