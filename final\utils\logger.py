"""
Logging utilities for Final RAG Model
"""
import logging
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime

from ..config import get_settings

settings = get_settings()


class ColoredFormatter(logging.Formatter):
    """Colored log formatter for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
            )
        
        return super().format(record)


def setup_logging(
    log_level: str = None,
    log_file: str = None,
    enable_console: bool = True,
    enable_file: bool = True
) -> logging.Logger:
    """
    Set up logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file
        enable_console: Enable console logging
        enable_file: Enable file logging
        
    Returns:
        Configured logger
    """
    # Use settings defaults if not provided
    log_level = log_level or settings.log_level
    log_file = log_file or settings.log_file
    
    # Create logger
    logger = logging.getLogger("final_rag")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    console_formatter = ColoredFormatter(
        fmt='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if enable_file and log_file:
        try:
            # Create log directory if it doesn't exist
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
        except Exception as e:
            logger.warning(f"Could not set up file logging: {str(e)}")
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    return logger


def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    if name is None:
        name = "final_rag"
    
    # Get or create logger
    logger = logging.getLogger(name)
    
    # If logger has no handlers, set it up
    if not logger.handlers:
        setup_logging()
        logger = logging.getLogger(name)
    
    return logger


class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or get_logger("performance")
        self.metrics = {}
    
    def log_timing(self, operation: str, duration_ms: float, **kwargs):
        """Log timing information"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"TIMING | {operation} | {duration_ms:.2f}ms"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.info(message)
        
        # Store in metrics
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append(duration_ms)
    
    def log_memory(self, operation: str, memory_mb: float, **kwargs):
        """Log memory usage"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"MEMORY | {operation} | {memory_mb:.2f}MB"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.info(message)
    
    def log_throughput(self, operation: str, items_per_second: float, **kwargs):
        """Log throughput metrics"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"THROUGHPUT | {operation} | {items_per_second:.2f} items/sec"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.info(message)
    
    def get_average_timing(self, operation: str) -> Optional[float]:
        """Get average timing for an operation"""
        if operation in self.metrics and self.metrics[operation]:
            return sum(self.metrics[operation]) / len(self.metrics[operation])
        return None
    
    def get_timing_stats(self, operation: str) -> dict:
        """Get timing statistics for an operation"""
        if operation not in self.metrics or not self.metrics[operation]:
            return {}
        
        timings = self.metrics[operation]
        return {
            "count": len(timings),
            "average": sum(timings) / len(timings),
            "min": min(timings),
            "max": max(timings),
            "total": sum(timings)
        }


class RequestLogger:
    """Logger for API requests"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or get_logger("requests")
    
    def log_request(self, method: str, path: str, **kwargs):
        """Log incoming request"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"REQUEST | {method} {path}"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.info(message)
    
    def log_response(self, method: str, path: str, status_code: int, 
                    duration_ms: float, **kwargs):
        """Log response"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"RESPONSE | {method} {path} | {status_code} | {duration_ms:.2f}ms"
        if extra_info:
            message += f" | {extra_info}"
        
        # Use different log levels based on status code
        if status_code >= 500:
            self.logger.error(message)
        elif status_code >= 400:
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def log_error(self, method: str, path: str, error: str, **kwargs):
        """Log request error"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        message = f"ERROR | {method} {path} | {error}"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.error(message)


# Global logger instances
_main_logger = None
_performance_logger = None
_request_logger = None


def get_main_logger() -> logging.Logger:
    """Get the main application logger"""
    global _main_logger
    if _main_logger is None:
        _main_logger = setup_logging()
    return _main_logger


def get_performance_logger() -> PerformanceLogger:
    """Get the performance logger"""
    global _performance_logger
    if _performance_logger is None:
        _performance_logger = PerformanceLogger()
    return _performance_logger


def get_request_logger() -> RequestLogger:
    """Get the request logger"""
    global _request_logger
    if _request_logger is None:
        _request_logger = RequestLogger()
    return _request_logger


# Initialize logging on import
if settings.enable_detailed_logging:
    setup_logging()
