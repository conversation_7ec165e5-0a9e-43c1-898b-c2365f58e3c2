# Final RAG Model Requirements
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI and ML Libraries
google-generativeai==0.3.2
faiss-cpu==1.7.4
numpy==1.24.3
scikit-learn==1.3.2

# Document Processing
PyPDF2==3.0.1
python-docx==1.1.0
python-multipart==0.0.6
aiofiles==23.2.1

# Text Processing
nltk==3.8.1
spacy==3.7.2
sentence-transformers==2.2.2

# Async and Concurrency
asyncio-throttle==1.0.2
aiohttp==3.9.1

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
rich==13.7.0
typer==0.9.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring and Performance
psutil==5.9.6
memory-profiler==0.61.0
