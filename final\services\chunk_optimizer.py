"""
Chunk Optimization Service for Final RAG Model
Smart chunk selection to process only necessary chunks and save time
"""
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict
import time
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Internal imports
from ..models.document import DocumentChunk
from ..models.query import Query, RankedChunk, SearchResult
from ..models.schemas import ChunkSelectionStrategy
from ..config import get_settings, get_chunk_config
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()
chunk_config = get_chunk_config()


class ChunkOptimizer:
    """Base chunk optimizer for efficient processing"""
    
    def __init__(self):
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.chunk_scores_cache = {}
        
    async def select_relevant_chunks(self, chunks: List[DocumentChunk], 
                                   query: Query, 
                                   max_chunks: int = None) -> List[DocumentChunk]:
        """
        Select most relevant chunks for processing
        
        Args:
            chunks: Available chunks
            query: Query to match against
            max_chunks: Maximum number of chunks to select
            
        Returns:
            Selected chunks for processing
        """
        try:
            if not chunks:
                return []
            
            max_chunks = max_chunks or settings.max_chunks_per_query
            
            # Quick filter by basic criteria
            filtered_chunks = self._filter_chunks_by_quality(chunks)
            
            if len(filtered_chunks) <= max_chunks:
                return filtered_chunks
            
            # Score and rank chunks
            ranked_chunks = await self._score_chunks(filtered_chunks, query)
            
            # Select top chunks
            selected_chunks = [rc.chunk for rc in ranked_chunks[:max_chunks]]
            
            logger.info(f"Selected {len(selected_chunks)} chunks from {len(chunks)} available")
            return selected_chunks
            
        except Exception as e:
            logger.error(f"Error selecting relevant chunks: {str(e)}")
            return chunks[:max_chunks] if max_chunks else chunks
    
    def _filter_chunks_by_quality(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Filter chunks by quality metrics"""
        filtered = []
        
        for chunk in chunks:
            # Quality checks
            if (len(chunk.content.strip()) < chunk_config.min_chunk_length or
                chunk.metadata.word_count < chunk_config.min_word_count):
                continue
            
            # Skip header-only chunks if configured
            if (chunk_config.exclude_headers_only and 
                self._is_header_only(chunk.content)):
                continue
            
            # Check quality score if available
            if (chunk.metadata.quality_score is not None and 
                chunk.metadata.quality_score < 0.3):
                continue
            
            filtered.append(chunk)
        
        return filtered
    
    def _is_header_only(self, content: str) -> bool:
        """Check if content is likely just a header"""
        lines = content.strip().split('\n')
        if len(lines) > 2:
            return False
        
        # Check for header patterns
        text = content.strip()
        if (len(text) < 50 and 
            (text.isupper() or text.istitle()) and
            not text.endswith(('.', '!', '?'))):
            return True
        
        return False
    
    async def _score_chunks(self, chunks: List[DocumentChunk], 
                          query: Query) -> List[RankedChunk]:
        """Score chunks based on relevance to query"""
        try:
            start_time = time.time()
            
            # Extract text content
            chunk_texts = [chunk.content for chunk in chunks]
            query_text = query.processed_text
            
            # Calculate TF-IDF similarity
            tfidf_scores = self._calculate_tfidf_similarity(chunk_texts, query_text)
            
            # Calculate keyword overlap
            keyword_scores = self._calculate_keyword_overlap(chunk_texts, query.keywords)
            
            # Calculate position scores (earlier chunks might be more important)
            position_scores = self._calculate_position_scores(chunks)
            
            # Create ranked chunks
            ranked_chunks = []
            for i, chunk in enumerate(chunks):
                # Get quality score
                quality_score = chunk.metadata.quality_score or 0.5
                
                # Combine scores
                semantic_score = tfidf_scores[i]
                keyword_score = keyword_scores[i]
                position_score = position_scores[i]
                
                # Calculate overall relevance
                relevance_score = (
                    semantic_score * 0.4 +
                    keyword_score * 0.3 +
                    quality_score * 0.2 +
                    position_score * 0.1
                )
                
                processing_time = (time.time() - start_time) * 1000 / len(chunks)
                
                ranked_chunk = RankedChunk(
                    chunk=chunk,
                    relevance_score=relevance_score,
                    semantic_score=semantic_score,
                    keyword_score=keyword_score,
                    position_score=position_score,
                    quality_score=quality_score,
                    processing_time_ms=processing_time
                )
                
                ranked_chunks.append(ranked_chunk)
            
            # Sort by relevance score
            ranked_chunks.sort(key=lambda x: x.relevance_score, reverse=True)
            
            processing_time = (time.time() - start_time) * 1000
            logger.debug(f"Scored {len(chunks)} chunks in {processing_time:.2f}ms")
            
            return ranked_chunks
            
        except Exception as e:
            logger.error(f"Error scoring chunks: {str(e)}")
            # Fallback: return chunks with default scores
            return [
                RankedChunk(
                    chunk=chunk,
                    relevance_score=0.5,
                    semantic_score=0.5,
                    keyword_score=0.5,
                    position_score=0.5,
                    quality_score=chunk.metadata.quality_score or 0.5,
                    processing_time_ms=1.0
                )
                for chunk in chunks
            ]
    
    def _calculate_tfidf_similarity(self, chunk_texts: List[str], query_text: str) -> List[float]:
        """Calculate TF-IDF similarity scores"""
        try:
            # Combine query with chunk texts for vectorization
            all_texts = [query_text] + chunk_texts
            
            # Fit and transform
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(all_texts)
            
            # Calculate similarities
            query_vector = tfidf_matrix[0:1]  # First row is query
            chunk_vectors = tfidf_matrix[1:]  # Rest are chunks
            
            similarities = cosine_similarity(query_vector, chunk_vectors)[0]
            
            return similarities.tolist()
            
        except Exception as e:
            logger.warning(f"Error calculating TF-IDF similarity: {str(e)}")
            return [0.5] * len(chunk_texts)
    
    def _calculate_keyword_overlap(self, chunk_texts: List[str], keywords: List[str]) -> List[float]:
        """Calculate keyword overlap scores"""
        if not keywords:
            return [0.0] * len(chunk_texts)
        
        scores = []
        for text in chunk_texts:
            text_lower = text.lower()
            matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
            score = matches / len(keywords) if keywords else 0.0
            scores.append(score)
        
        return scores
    
    def _calculate_position_scores(self, chunks: List[DocumentChunk]) -> List[float]:
        """Calculate position-based scores (earlier chunks get higher scores)"""
        if not chunks:
            return []
        
        scores = []
        for chunk in chunks:
            # Normalize position (0-1, where 0 is first chunk)
            position_ratio = chunk.metadata.chunk_index / max(1, len(chunks) - 1)
            # Invert so earlier chunks get higher scores
            position_score = 1.0 - (position_ratio * 0.3)  # Max 30% penalty for position
            scores.append(max(0.7, position_score))  # Minimum score of 0.7
        
        return scores


class SmartChunkSelector(ChunkOptimizer):
    """Advanced chunk selector with multiple strategies"""
    
    def __init__(self):
        super().__init__()
        self.selection_strategies = {
            ChunkSelectionStrategy.SEMANTIC: self._semantic_selection,
            ChunkSelectionStrategy.KEYWORD: self._keyword_selection,
            ChunkSelectionStrategy.HYBRID: self._hybrid_selection,
            ChunkSelectionStrategy.SMART: self._smart_selection
        }
    
    async def select_chunks_with_strategy(self, chunks: List[DocumentChunk],
                                        query: Query,
                                        strategy: ChunkSelectionStrategy,
                                        max_chunks: int = None) -> List[DocumentChunk]:
        """
        Select chunks using specified strategy
        
        Args:
            chunks: Available chunks
            query: Query to match against
            strategy: Selection strategy to use
            max_chunks: Maximum chunks to select
            
        Returns:
            Selected chunks
        """
        try:
            max_chunks = max_chunks or settings.max_chunks_per_query
            
            if strategy not in self.selection_strategies:
                logger.warning(f"Unknown strategy {strategy}, using smart selection")
                strategy = ChunkSelectionStrategy.SMART
            
            selection_func = self.selection_strategies[strategy]
            selected_chunks = await selection_func(chunks, query, max_chunks)
            
            logger.info(f"Selected {len(selected_chunks)} chunks using {strategy} strategy")
            return selected_chunks
            
        except Exception as e:
            logger.error(f"Error in chunk selection: {str(e)}")
            return chunks[:max_chunks] if max_chunks else chunks
    
    async def _semantic_selection(self, chunks: List[DocumentChunk], 
                                query: Query, max_chunks: int) -> List[DocumentChunk]:
        """Select chunks based on semantic similarity"""
        filtered_chunks = self._filter_chunks_by_quality(chunks)
        
        if len(filtered_chunks) <= max_chunks:
            return filtered_chunks
        
        # Score by semantic similarity only
        chunk_texts = [chunk.content for chunk in filtered_chunks]
        tfidf_scores = self._calculate_tfidf_similarity(chunk_texts, query.processed_text)
        
        # Sort by semantic score
        scored_chunks = list(zip(filtered_chunks, tfidf_scores))
        scored_chunks.sort(key=lambda x: x[1], reverse=True)
        
        return [chunk for chunk, _ in scored_chunks[:max_chunks]]
    
    async def _keyword_selection(self, chunks: List[DocumentChunk], 
                               query: Query, max_chunks: int) -> List[DocumentChunk]:
        """Select chunks based on keyword matching"""
        filtered_chunks = self._filter_chunks_by_quality(chunks)
        
        if len(filtered_chunks) <= max_chunks:
            return filtered_chunks
        
        # Score by keyword overlap only
        chunk_texts = [chunk.content for chunk in filtered_chunks]
        keyword_scores = self._calculate_keyword_overlap(chunk_texts, query.keywords)
        
        # Sort by keyword score
        scored_chunks = list(zip(filtered_chunks, keyword_scores))
        scored_chunks.sort(key=lambda x: x[1], reverse=True)
        
        return [chunk for chunk, _ in scored_chunks[:max_chunks]]
    
    async def _hybrid_selection(self, chunks: List[DocumentChunk], 
                              query: Query, max_chunks: int) -> List[DocumentChunk]:
        """Select chunks using hybrid semantic + keyword approach"""
        return await self.select_relevant_chunks(chunks, query, max_chunks)
    
    async def _smart_selection(self, chunks: List[DocumentChunk], 
                             query: Query, max_chunks: int) -> List[DocumentChunk]:
        """Smart selection with adaptive strategy based on query type"""
        # Analyze query to determine best strategy
        if query.query_type and query.complexity:
            # Adjust strategy based on query characteristics
            if len(query.keywords) > 5:
                # Many keywords - use keyword-heavy approach
                return await self._keyword_heavy_selection(chunks, query, max_chunks)
            elif query.complexity.value == "complex":
                # Complex query - use semantic approach
                return await self._semantic_selection(chunks, query, max_chunks)
            else:
                # Default hybrid approach
                return await self._hybrid_selection(chunks, query, max_chunks)
        
        # Fallback to hybrid
        return await self._hybrid_selection(chunks, query, max_chunks)
    
    async def _keyword_heavy_selection(self, chunks: List[DocumentChunk], 
                                     query: Query, max_chunks: int) -> List[DocumentChunk]:
        """Selection strategy that heavily weights keyword matching"""
        filtered_chunks = self._filter_chunks_by_quality(chunks)
        
        if len(filtered_chunks) <= max_chunks:
            return filtered_chunks
        
        # Score with heavy keyword weighting
        chunk_texts = [chunk.content for chunk in filtered_chunks]
        tfidf_scores = self._calculate_tfidf_similarity(chunk_texts, query.processed_text)
        keyword_scores = self._calculate_keyword_overlap(chunk_texts, query.keywords)
        
        # Combine with heavy keyword weighting
        combined_scores = [
            (tfidf * 0.2 + keyword * 0.8) 
            for tfidf, keyword in zip(tfidf_scores, keyword_scores)
        ]
        
        # Sort by combined score
        scored_chunks = list(zip(filtered_chunks, combined_scores))
        scored_chunks.sort(key=lambda x: x[1], reverse=True)
        
        return [chunk for chunk, _ in scored_chunks[:max_chunks]]
