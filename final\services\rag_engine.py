"""
RAG Engine - Main orchestrator for the Final RAG Model
Coordinates all services for optimized document processing and query answering
"""
import asyncio
import time
import uuid
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

# Internal imports
from ..models.schemas import RAGRequest, RAGResponse, ProcessingStatus, ChunkSelectionStrategy
from ..models.document import Document, DocumentChunk
from ..models.query import Query
from ..models.response import (
    RAGProcessingResult, ProcessingStatistics, OptimizationReport,
    AnswerGeneration, ChunkProcessingResult
)
from ..services.document_processor import SmartDocumentProcessor
from ..services.embedding_service import OptimizedEmbeddingService
from ..services.vector_store import OptimizedVectorStore
from ..services.query_processor import SmartQueryProcessor
from ..services.chunk_optimizer import SmartChunkSelector
from ..config import get_settings
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class RAGEngine:
    """Main RAG engine that orchestrates all components"""
    
    def __init__(self):
        # Initialize services
        self.document_processor = SmartDocumentProcessor()
        self.embedding_service = OptimizedEmbeddingService()
        self.vector_store = OptimizedVectorStore(self.embedding_service)
        self.query_processor = SmartQueryProcessor(self.vector_store)
        self.chunk_selector = SmartChunkSelector()
        
        # Processing state
        self.is_initialized = False
        self.current_document = None
        self.processing_stats = ProcessingStatistics()
        
    async def initialize(self):
        """Initialize all services"""
        try:
            logger.info("Initializing RAG Engine...")
            start_time = time.time()
            
            # Initialize services in order
            await self.embedding_service.initialize()
            await self.vector_store.initialize()
            await self.query_processor.initialize()
            
            self.is_initialized = True
            
            init_time = (time.time() - start_time) * 1000
            logger.info(f"RAG Engine initialized in {init_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"Error initializing RAG Engine: {str(e)}")
            raise
    
    async def process_request(self, request: RAGRequest) -> RAGResponse:
        """
        Process a complete RAG request
        
        Args:
            request: RAG request with documents and questions
            
        Returns:
            Complete RAG response
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            request_id = str(uuid.uuid4())
            logger.info(f"Processing RAG request {request_id}")
            start_time = time.time()
            
            # Reset processing stats
            self.processing_stats = ProcessingStatistics()
            
            # Process documents
            documents = await self._process_documents(request.documents)
            
            # Create and process queries
            queries = [
                Query(
                    id=str(uuid.uuid4()),
                    text=question,
                    processed_text=question.strip().lower()
                )
                for question in request.questions
            ]
            
            # Get all chunks from documents
            all_chunks = []
            for doc in documents:
                all_chunks.extend(doc.chunks)
            
            # Add chunks to vector store
            if all_chunks:
                await self.vector_store.add_chunks(all_chunks)
            
            # Process queries with optimization
            answers = await self._process_queries_optimized(
                queries, 
                all_chunks,
                request.chunk_strategy,
                request.max_chunks,
                request.similarity_threshold
            )
            
            # Calculate processing time
            total_time = (time.time() - start_time) * 1000
            
            # Update statistics
            self.processing_stats.total_processing_time_ms = total_time
            self.processing_stats.documents_processed = len(documents)
            self.processing_stats.total_chunks_created = len(all_chunks)
            
            # Create response
            response = RAGResponse(
                request_id=request_id,
                status=ProcessingStatus.COMPLETED,
                answers=answers,
                total_processing_time_ms=total_time,
                documents_processed=len(documents),
                total_chunks_created=len(all_chunks),
                average_chunk_size=sum(len(chunk.content) for chunk in all_chunks) / len(all_chunks) if all_chunks else 0,
                embedding_time_ms=self.processing_stats.average_generation_time_ms * len(all_chunks),
                search_time_ms=self.processing_stats.average_search_time_ms * len(queries),
                generation_time_ms=self.processing_stats.average_generation_time_ms * len(queries)
            )
            
            logger.info(f"RAG request {request_id} completed in {total_time:.2f}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error processing RAG request: {str(e)}")
            raise
    
    async def _process_documents(self, document_sources: Union[str, List[str]]) -> List[Document]:
        """Process document sources into Document objects"""
        try:
            if isinstance(document_sources, str):
                document_sources = [document_sources]
            
            logger.info(f"Processing {len(document_sources)} documents")
            
            # Process documents concurrently
            tasks = [
                self.document_processor.process_document_with_chunks(source)
                for source in document_sources
            ]
            
            documents = await asyncio.gather(*tasks)
            
            logger.info(f"Processed {len(documents)} documents successfully")
            return documents
            
        except Exception as e:
            logger.error(f"Error processing documents: {str(e)}")
            raise
    
    async def _process_queries_optimized(self, queries: List[Query],
                                       all_chunks: List[DocumentChunk],
                                       strategy: ChunkSelectionStrategy,
                                       max_chunks: Optional[int],
                                       similarity_threshold: Optional[float]) -> List:
        """Process queries with optimization"""
        try:
            logger.info(f"Processing {len(queries)} queries with {strategy} strategy")
            start_time = time.time()
            
            answers = []
            total_chunks_processed = 0
            
            for query in queries:
                query_start = time.time()
                
                # Analyze query
                query.extract_keywords()
                query.analyze_complexity()
                query.detect_query_type()
                
                # Select relevant chunks using strategy
                if settings.enable_smart_chunking:
                    selected_chunks = await self.chunk_selector.select_chunks_with_strategy(
                        all_chunks, query, strategy, max_chunks
                    )
                else:
                    # Fallback to basic selection
                    selected_chunks = all_chunks[:max_chunks] if max_chunks else all_chunks
                
                total_chunks_processed += len(selected_chunks)
                
                # Process query
                answer = await self.query_processor.process_query(query, selected_chunks)
                answers.append(answer)
                
                query_time = (time.time() - query_start) * 1000
                logger.debug(f"Query processed in {query_time:.2f}ms using {len(selected_chunks)} chunks")
            
            # Update statistics
            processing_time = (time.time() - start_time) * 1000
            self.processing_stats.chunks_processed = total_chunks_processed
            self.processing_stats.total_searches = len(queries)
            self.processing_stats.average_search_time_ms = processing_time / len(queries)
            
            # Calculate optimization effectiveness
            total_available = len(all_chunks) * len(queries)
            selection_ratio = total_chunks_processed / total_available if total_available > 0 else 1.0
            
            logger.info(f"Processed {len(queries)} queries in {processing_time:.2f}ms "
                       f"(efficiency: {selection_ratio:.2%})")
            
            return answers
            
        except Exception as e:
            logger.error(f"Error in optimized query processing: {str(e)}")
            raise
    
    async def process_single_query(self, query_text: str, 
                                 document_source: str = None) -> Dict[str, Any]:
        """
        Process a single query (convenience method)
        
        Args:
            query_text: Question to answer
            document_source: Optional document URL/content
            
        Returns:
            Answer dictionary
        """
        try:
            # Create request
            request = RAGRequest(
                documents=document_source or "",
                questions=[query_text],
                chunk_strategy=ChunkSelectionStrategy.SMART,
                max_chunks=settings.max_chunks_per_query,
                similarity_threshold=settings.similarity_threshold
            )
            
            # Process request
            if document_source:
                response = await self.process_request(request)
            else:
                # Use existing vector store
                query = Query(
                    id=str(uuid.uuid4()),
                    text=query_text,
                    processed_text=query_text.strip().lower()
                )
                
                answer = await self.query_processor.process_query(query)
                return {
                    "question": query_text,
                    "answer": answer.answer,
                    "confidence": answer.confidence.overall,
                    "sources": len(answer.sources),
                    "processing_time_ms": answer.processing_time_ms
                }
            
            # Extract first answer
            if response.answers:
                answer = response.answers[0]
                return {
                    "question": query_text,
                    "answer": answer.answer,
                    "confidence": answer.confidence.overall,
                    "sources": len(answer.sources),
                    "processing_time_ms": answer.processing_time_ms,
                    "chunks_processed": answer.chunks_processed
                }
            else:
                return {
                    "question": query_text,
                    "answer": "No answer could be generated.",
                    "confidence": 0.0,
                    "sources": 0,
                    "processing_time_ms": 0
                }
                
        except Exception as e:
            logger.error(f"Error processing single query: {str(e)}")
            return {
                "question": query_text,
                "answer": f"Error: {str(e)}",
                "confidence": 0.0,
                "sources": 0,
                "processing_time_ms": 0
            }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        try:
            return {
                "engine_status": "initialized" if self.is_initialized else "not_initialized",
                "processing_stats": {
                    "documents_processed": self.processing_stats.documents_processed,
                    "chunks_processed": self.processing_stats.chunks_processed,
                    "total_searches": self.processing_stats.total_searches,
                    "processing_efficiency": self.processing_stats.processing_efficiency
                },
                "vector_store_stats": self.vector_store.get_stats(),
                "embedding_cache_stats": self.embedding_service.get_cache_stats(),
                "search_cache_stats": self.vector_store.get_cache_stats(),
                "configuration": {
                    "chunk_size": settings.chunk_size,
                    "max_chunks_per_query": settings.max_chunks_per_query,
                    "similarity_threshold": settings.similarity_threshold,
                    "smart_chunking_enabled": settings.enable_smart_chunking,
                    "cache_enabled": settings.cache_embeddings
                }
            }
        except Exception as e:
            logger.error(f"Error getting system stats: {str(e)}")
            return {"error": str(e)}
    
    async def clear_all_caches(self):
        """Clear all caches"""
        try:
            self.embedding_service.clear_cache()
            self.vector_store.clear_cache()
            logger.info("All caches cleared")
        except Exception as e:
            logger.error(f"Error clearing caches: {str(e)}")
            raise
