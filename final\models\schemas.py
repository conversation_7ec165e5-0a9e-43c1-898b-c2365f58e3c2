"""
Pydantic models for the Final RAG Model
Optimized for efficient chunk processing and high performance
"""
from pydantic import BaseModel, Field, HttpUrl, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class ProcessingStatus(str, Enum):
    """Status of document processing"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ChunkSelectionStrategy(str, Enum):
    """Strategy for chunk selection"""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    SMART = "smart"


class RAGRequest(BaseModel):
    """Main request model for RAG processing"""
    documents: Union[str, List[str]] = Field(
        ..., 
        description="URL(s) to documents (PDF/DOCX/TXT) or document content"
    )
    questions: List[str] = Field(
        ..., 
        description="List of questions to answer",
        min_items=1,
        max_items=10
    )
    
    # Processing Options
    chunk_strategy: ChunkSelectionStrategy = Field(
        default=ChunkSelectionStrategy.SMART,
        description="Strategy for chunk selection"
    )
    max_chunks: Optional[int] = Field(
        default=5,
        description="Maximum number of chunks to process per query",
        ge=1,
        le=20
    )
    similarity_threshold: Optional[float] = Field(
        default=0.7,
        description="Minimum similarity threshold for chunk selection",
        ge=0.0,
        le=1.0
    )
    
    # Response Options
    include_sources: bool = Field(default=True, description="Include source references")
    include_confidence: bool = Field(default=True, description="Include confidence scores")
    include_reasoning: bool = Field(default=True, description="Include reasoning explanation")
    
    class Config:
        json_schema_extra = {
            "example": {
                "documents": "https://example.com/policy.pdf",
                "questions": [
                    "What is the grace period for premium payment?",
                    "What are the exclusions in this policy?"
                ],
                "chunk_strategy": "smart",
                "max_chunks": 5,
                "similarity_threshold": 0.7,
                "include_sources": True,
                "include_confidence": True,
                "include_reasoning": True
            }
        }


class BatchRAGRequest(BaseModel):
    """Batch processing request for multiple document sets"""
    requests: List[RAGRequest] = Field(
        ...,
        description="List of RAG requests to process",
        min_items=1,
        max_items=5
    )
    parallel_processing: bool = Field(
        default=True,
        description="Enable parallel processing of requests"
    )


class ConfidenceScore(BaseModel):
    """Confidence score with breakdown"""
    overall: float = Field(..., description="Overall confidence score (0-1)", ge=0.0, le=1.0)
    semantic_similarity: float = Field(..., description="Semantic similarity score", ge=0.0, le=1.0)
    keyword_match: float = Field(..., description="Keyword matching score", ge=0.0, le=1.0)
    context_relevance: float = Field(..., description="Context relevance score", ge=0.0, le=1.0)
    
    @validator('overall')
    def validate_overall_score(cls, v, values):
        """Ensure overall score is reasonable based on components"""
        if 'semantic_similarity' in values and 'keyword_match' in values:
            min_expected = min(values['semantic_similarity'], values['keyword_match']) * 0.8
            max_expected = max(values['semantic_similarity'], values['keyword_match']) * 1.1
            if not (min_expected <= v <= max_expected):
                # Allow but log warning for unusual scores
                pass
        return v


class SourceReference(BaseModel):
    """Reference to source document and chunk"""
    document_id: str = Field(..., description="Document identifier")
    chunk_id: str = Field(..., description="Chunk identifier")
    chunk_text: str = Field(..., description="Relevant chunk text")
    page_number: Optional[int] = Field(None, description="Page number if available")
    section: Optional[str] = Field(None, description="Document section")
    similarity_score: float = Field(..., description="Similarity score", ge=0.0, le=1.0)
    
    class Config:
        json_schema_extra = {
            "example": {
                "document_id": "doc_123",
                "chunk_id": "chunk_456",
                "chunk_text": "The grace period for premium payment is 30 days...",
                "page_number": 5,
                "section": "Premium Payment Terms",
                "similarity_score": 0.92
            }
        }


class Answer(BaseModel):
    """Basic answer model"""
    question: str = Field(..., description="The original question")
    answer: str = Field(..., description="Generated answer")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")
    
    class Config:
        json_schema_extra = {
            "example": {
                "question": "What is the grace period?",
                "answer": "The grace period for premium payment is 30 days.",
                "processing_time_ms": 245.7
            }
        }


class AnswerWithExplanation(Answer):
    """Extended answer model with detailed explanation"""
    confidence: ConfidenceScore = Field(..., description="Confidence breakdown")
    sources: List[SourceReference] = Field(..., description="Source references")
    reasoning: str = Field(..., description="Explanation of reasoning")
    chunks_processed: int = Field(..., description="Number of chunks processed")
    total_chunks_available: int = Field(..., description="Total chunks available")
    
    class Config:
        json_schema_extra = {
            "example": {
                "question": "What is the grace period?",
                "answer": "The grace period for premium payment is 30 days.",
                "processing_time_ms": 245.7,
                "confidence": {
                    "overall": 0.95,
                    "semantic_similarity": 0.92,
                    "keyword_match": 0.88,
                    "context_relevance": 0.94
                },
                "sources": [
                    {
                        "document_id": "doc_123",
                        "chunk_id": "chunk_456",
                        "chunk_text": "Premium payment grace period...",
                        "similarity_score": 0.92
                    }
                ],
                "reasoning": "Based on section 4.2 of the policy document...",
                "chunks_processed": 3,
                "total_chunks_available": 45
            }
        }


class RAGResponse(BaseModel):
    """Main response model for RAG processing"""
    request_id: str = Field(..., description="Unique request identifier")
    status: ProcessingStatus = Field(..., description="Processing status")
    answers: List[AnswerWithExplanation] = Field(..., description="Generated answers")
    total_processing_time_ms: float = Field(..., description="Total processing time")
    
    # Processing Statistics
    documents_processed: int = Field(..., description="Number of documents processed")
    total_chunks_created: int = Field(..., description="Total chunks created")
    average_chunk_size: float = Field(..., description="Average chunk size in characters")
    
    # Performance Metrics
    embedding_time_ms: float = Field(..., description="Time spent on embeddings")
    search_time_ms: float = Field(..., description="Time spent on search")
    generation_time_ms: float = Field(..., description="Time spent on answer generation")
    
    class Config:
        json_schema_extra = {
            "example": {
                "request_id": "req_789",
                "status": "completed",
                "answers": [],
                "total_processing_time_ms": 1250.5,
                "documents_processed": 1,
                "total_chunks_created": 45,
                "average_chunk_size": 487.3,
                "embedding_time_ms": 450.2,
                "search_time_ms": 125.8,
                "generation_time_ms": 674.5
            }
        }


class BatchRAGResponse(BaseModel):
    """Response model for batch processing"""
    batch_id: str = Field(..., description="Batch identifier")
    responses: List[RAGResponse] = Field(..., description="Individual responses")
    total_processing_time_ms: float = Field(..., description="Total batch processing time")
    successful_requests: int = Field(..., description="Number of successful requests")
    failed_requests: int = Field(..., description="Number of failed requests")


class ErrorResponse(BaseModel):
    """Error response model"""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error_code": "DOCUMENT_PROCESSING_FAILED",
                "error_message": "Failed to process document: Invalid PDF format",
                "details": {"document_url": "https://example.com/doc.pdf"},
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }
