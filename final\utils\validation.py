"""
Validation utilities for Final RAG Model
"""
import re
import validators
from typing import List, Dict, Any, Optional, Union
from urllib.parse import urlparse
import aiohttp

from ..models.schemas import RAGRequest, ChunkSelectionStrategy
from .logger import get_logger

logger = get_logger(__name__)


class ValidationError(Exception):
    """Custom validation error"""
    pass


class RequestValidator:
    """Validator for API requests"""
    
    def __init__(self):
        self.max_questions = 10
        self.max_question_length = 1000
        self.max_documents = 5
        self.supported_schemes = {'http', 'https'}
        self.supported_extensions = {'.pdf', '.docx', '.txt', '.html', '.md'}
    
    def validate_rag_request(self, request: RAGRequest) -> Dict[str, Any]:
        """
        Validate RAG request
        
        Args:
            request: RAG request to validate
            
        Returns:
            Validation result dictionary
            
        Raises:
            ValidationError: If validation fails
        """
        errors = []
        warnings = []
        
        # Validate documents
        doc_validation = self._validate_documents(request.documents)
        errors.extend(doc_validation.get('errors', []))
        warnings.extend(doc_validation.get('warnings', []))
        
        # Validate questions
        question_validation = self._validate_questions(request.questions)
        errors.extend(question_validation.get('errors', []))
        warnings.extend(question_validation.get('warnings', []))
        
        # Validate parameters
        param_validation = self._validate_parameters(request)
        errors.extend(param_validation.get('errors', []))
        warnings.extend(param_validation.get('warnings', []))
        
        # Check for critical errors
        if errors:
            error_msg = "; ".join(errors)
            raise ValidationError(f"Request validation failed: {error_msg}")
        
        return {
            "valid": True,
            "warnings": warnings,
            "document_count": len(request.documents) if isinstance(request.documents, list) else 1,
            "question_count": len(request.questions)
        }
    
    def _validate_documents(self, documents: Union[str, List[str]]) -> Dict[str, List[str]]:
        """Validate document sources"""
        errors = []
        warnings = []
        
        if not documents:
            errors.append("No documents provided")
            return {"errors": errors, "warnings": warnings}
        
        # Convert to list for uniform processing
        if isinstance(documents, str):
            doc_list = [documents]
        else:
            doc_list = documents
        
        if len(doc_list) > self.max_documents:
            errors.append(f"Too many documents (max {self.max_documents})")
        
        for i, doc in enumerate(doc_list):
            if not doc or not doc.strip():
                errors.append(f"Document {i+1} is empty")
                continue
            
            # Validate URL format
            url_validation = self._validate_url(doc.strip())
            if not url_validation["valid"]:
                errors.append(f"Document {i+1}: {url_validation['error']}")
            elif url_validation.get("warnings"):
                warnings.extend([f"Document {i+1}: {w}" for w in url_validation["warnings"]])
        
        return {"errors": errors, "warnings": warnings}
    
    def _validate_questions(self, questions: List[str]) -> Dict[str, List[str]]:
        """Validate questions"""
        errors = []
        warnings = []
        
        if not questions:
            errors.append("No questions provided")
            return {"errors": errors, "warnings": warnings}
        
        if len(questions) > self.max_questions:
            errors.append(f"Too many questions (max {self.max_questions})")
        
        for i, question in enumerate(questions):
            if not question or not question.strip():
                errors.append(f"Question {i+1} is empty")
                continue
            
            if len(question) > self.max_question_length:
                errors.append(f"Question {i+1} too long (max {self.max_question_length} chars)")
            
            # Check for very short questions
            if len(question.strip()) < 5:
                warnings.append(f"Question {i+1} is very short")
            
            # Check for question marks (optional warning)
            if not question.strip().endswith('?'):
                warnings.append(f"Question {i+1} doesn't end with '?'")
        
        return {"errors": errors, "warnings": warnings}
    
    def _validate_parameters(self, request: RAGRequest) -> Dict[str, List[str]]:
        """Validate request parameters"""
        errors = []
        warnings = []
        
        # Validate chunk strategy
        if request.chunk_strategy not in ChunkSelectionStrategy:
            errors.append(f"Invalid chunk strategy: {request.chunk_strategy}")
        
        # Validate max_chunks
        if request.max_chunks is not None:
            if request.max_chunks < 1:
                errors.append("max_chunks must be at least 1")
            elif request.max_chunks > 20:
                warnings.append("max_chunks is very high, may impact performance")
        
        # Validate similarity_threshold
        if request.similarity_threshold is not None:
            if not (0.0 <= request.similarity_threshold <= 1.0):
                errors.append("similarity_threshold must be between 0.0 and 1.0")
            elif request.similarity_threshold < 0.3:
                warnings.append("Very low similarity threshold may return irrelevant results")
            elif request.similarity_threshold > 0.9:
                warnings.append("Very high similarity threshold may return too few results")
        
        return {"errors": errors, "warnings": warnings}
    
    def _validate_url(self, url: str) -> Dict[str, Any]:
        """Validate URL format and accessibility"""
        try:
            # Basic URL validation
            if not validators.url(url):
                return {"valid": False, "error": "Invalid URL format"}
            
            # Parse URL
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in self.supported_schemes:
                return {"valid": False, "error": f"Unsupported URL scheme: {parsed.scheme}"}
            
            # Check for file extension (if present)
            warnings = []
            if parsed.path:
                path_lower = parsed.path.lower()
                has_supported_ext = any(path_lower.endswith(ext) for ext in self.supported_extensions)
                if not has_supported_ext and '.' in parsed.path:
                    warnings.append("File extension may not be supported")
            
            return {"valid": True, "warnings": warnings}
            
        except Exception as e:
            return {"valid": False, "error": f"URL validation error: {str(e)}"}


async def validate_document_url(url: str, timeout: int = 10) -> Dict[str, Any]:
    """
    Validate document URL accessibility
    
    Args:
        url: URL to validate
        timeout: Request timeout in seconds
        
    Returns:
        Validation result dictionary
    """
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.head(url) as response:
                result = {
                    "accessible": response.status == 200,
                    "status_code": response.status,
                    "content_type": response.headers.get('content-type', ''),
                    "content_length": response.headers.get('content-length'),
                }
                
                # Check content type
                content_type = result["content_type"].lower()
                if content_type:
                    if 'pdf' in content_type:
                        result["document_type"] = "pdf"
                    elif 'word' in content_type or 'docx' in content_type:
                        result["document_type"] = "docx"
                    elif 'text' in content_type:
                        result["document_type"] = "txt"
                    elif 'html' in content_type:
                        result["document_type"] = "html"
                    else:
                        result["document_type"] = "unknown"
                        result["warning"] = f"Unknown content type: {content_type}"
                
                return result
                
    except asyncio.TimeoutError:
        return {"accessible": False, "error": "Request timeout"}
    except Exception as e:
        return {"accessible": False, "error": str(e)}


def validate_text_content(text: str, min_length: int = 10, max_length: int = 100000) -> Dict[str, Any]:
    """
    Validate text content
    
    Args:
        text: Text to validate
        min_length: Minimum text length
        max_length: Maximum text length
        
    Returns:
        Validation result
    """
    if not text:
        return {"valid": False, "error": "Text is empty"}
    
    text = text.strip()
    
    if len(text) < min_length:
        return {"valid": False, "error": f"Text too short (min {min_length} chars)"}
    
    if len(text) > max_length:
        return {"valid": False, "error": f"Text too long (max {max_length} chars)"}
    
    # Check for meaningful content
    word_count = len(text.split())
    if word_count < 3:
        return {"valid": False, "error": "Text must contain at least 3 words"}
    
    # Check character diversity (avoid repeated characters)
    unique_chars = len(set(text.lower()))
    if unique_chars < 10:
        return {"valid": False, "error": "Text lacks character diversity"}
    
    return {
        "valid": True,
        "length": len(text),
        "word_count": word_count,
        "unique_chars": unique_chars
    }


def validate_query_text(query: str) -> Dict[str, Any]:
    """
    Validate query text
    
    Args:
        query: Query text to validate
        
    Returns:
        Validation result
    """
    if not query:
        return {"valid": False, "error": "Query is empty"}
    
    query = query.strip()
    
    if len(query) < 3:
        return {"valid": False, "error": "Query too short"}
    
    if len(query) > 1000:
        return {"valid": False, "error": "Query too long"}
    
    # Check for meaningful content
    if not re.search(r'[a-zA-Z]', query):
        return {"valid": False, "error": "Query must contain letters"}
    
    return {
        "valid": True,
        "length": len(query),
        "word_count": len(query.split())
    }


# Global validator instance
_request_validator = RequestValidator()


def validate_request(request: RAGRequest) -> Dict[str, Any]:
    """Validate RAG request using global validator"""
    return _request_validator.validate_rag_request(request)


def get_request_validator() -> RequestValidator:
    """Get the global request validator"""
    return _request_validator
