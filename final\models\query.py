"""
Query-related models for the Final RAG Model
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from enum import Enum
import re
from .document import DocumentChunk


class QueryType(str, Enum):
    """Types of queries"""
    FACTUAL = "factual"
    ANALYTICAL = "analytical"
    COMPARATIVE = "comparative"
    PROCEDURAL = "procedural"
    DEFINITIONAL = "definitional"


class QueryComplexity(str, Enum):
    """Query complexity levels"""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class Query(BaseModel):
    """Enhanced query model with analysis capabilities"""
    id: str = Field(..., description="Unique query identifier")
    text: str = Field(..., description="Original query text")
    processed_text: str = Field(..., description="Processed/cleaned query text")
    
    # Query analysis
    query_type: Optional[QueryType] = Field(None, description="Detected query type")
    complexity: Optional[QueryComplexity] = Field(None, description="Query complexity")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    entities: List[str] = Field(default_factory=list, description="Named entities")
    
    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    language: Optional[str] = Field(None, description="Detected language")
    
    # Optimization flags
    requires_multi_hop: bool = Field(default=False, description="Requires multi-hop reasoning")
    requires_aggregation: bool = Field(default=False, description="Requires data aggregation")
    
    @validator('text')
    def validate_text(cls, v):
        """Validate query text"""
        if not v or not v.strip():
            raise ValueError("Query text cannot be empty")
        if len(v) > 1000:
            raise ValueError("Query text too long (max 1000 characters)")
        return v.strip()
    
    def extract_keywords(self) -> List[str]:
        """Extract keywords from query text"""
        # Simple keyword extraction (can be enhanced with NLP)
        text = self.processed_text.lower()
        
        # Remove common stop words
        stop_words = {
            'what', 'is', 'are', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 
            'to', 'for', 'of', 'with', 'by', 'how', 'when', 'where', 'why', 'which',
            'can', 'could', 'would', 'should', 'will', 'do', 'does', 'did', 'have', 'has'
        }
        
        # Extract words (alphanumeric sequences)
        words = re.findall(r'\b[a-zA-Z0-9]+\b', text)
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        self.keywords = list(set(keywords))  # Remove duplicates
        return self.keywords
    
    def analyze_complexity(self) -> QueryComplexity:
        """Analyze query complexity"""
        text = self.processed_text.lower()
        
        # Complexity indicators
        complex_indicators = ['compare', 'contrast', 'analyze', 'evaluate', 'explain why', 'how does']
        moderate_indicators = ['list', 'describe', 'what are', 'how many', 'when did']
        
        if any(indicator in text for indicator in complex_indicators):
            complexity = QueryComplexity.COMPLEX
        elif any(indicator in text for indicator in moderate_indicators):
            complexity = QueryComplexity.MODERATE
        else:
            complexity = QueryComplexity.SIMPLE
        
        self.complexity = complexity
        return complexity
    
    def detect_query_type(self) -> QueryType:
        """Detect the type of query"""
        text = self.processed_text.lower()
        
        # Type detection patterns
        if any(word in text for word in ['what is', 'define', 'definition', 'meaning']):
            query_type = QueryType.DEFINITIONAL
        elif any(word in text for word in ['how to', 'steps', 'procedure', 'process']):
            query_type = QueryType.PROCEDURAL
        elif any(word in text for word in ['compare', 'difference', 'versus', 'vs']):
            query_type = QueryType.COMPARATIVE
        elif any(word in text for word in ['analyze', 'why', 'because', 'reason']):
            query_type = QueryType.ANALYTICAL
        else:
            query_type = QueryType.FACTUAL
        
        self.query_type = query_type
        return query_type


class QueryContext(BaseModel):
    """Context for query processing"""
    query: Query = Field(..., description="The query being processed")
    document_ids: List[str] = Field(default_factory=list, description="Relevant document IDs")
    
    # Search parameters
    max_chunks: int = Field(default=5, description="Maximum chunks to retrieve")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold")
    
    # Processing options
    enable_reranking: bool = Field(default=True, description="Enable chunk reranking")
    enable_expansion: bool = Field(default=False, description="Enable query expansion")
    
    # Context window management
    max_context_length: int = Field(default=4000, description="Maximum context length")
    context_overlap: int = Field(default=100, description="Context overlap in characters")


class SearchResult(BaseModel):
    """Result from vector search"""
    chunk: DocumentChunk = Field(..., description="Retrieved chunk")
    score: float = Field(..., description="Similarity score", ge=0.0, le=1.0)
    rank: int = Field(..., description="Rank in search results", ge=1)
    
    # Additional scoring
    keyword_score: Optional[float] = Field(None, description="Keyword matching score")
    semantic_score: Optional[float] = Field(None, description="Semantic similarity score")
    final_score: Optional[float] = Field(None, description="Final combined score")
    
    # Relevance indicators
    has_exact_match: bool = Field(default=False, description="Contains exact keyword match")
    has_partial_match: bool = Field(default=False, description="Contains partial keyword match")
    context_relevance: Optional[float] = Field(None, description="Context relevance score")


class RankedChunk(BaseModel):
    """Chunk with ranking information for efficient processing"""
    chunk: DocumentChunk = Field(..., description="The document chunk")
    relevance_score: float = Field(..., description="Overall relevance score", ge=0.0, le=1.0)
    
    # Scoring breakdown
    semantic_score: float = Field(..., description="Semantic similarity score")
    keyword_score: float = Field(..., description="Keyword matching score")
    position_score: float = Field(..., description="Position-based score")
    quality_score: float = Field(..., description="Content quality score")
    
    # Processing metadata
    processing_time_ms: float = Field(..., description="Time to process this chunk")
    selected_for_context: bool = Field(default=False, description="Selected for final context")
    
    @property
    def combined_score(self) -> float:
        """Calculate combined score with weights"""
        return (
            self.semantic_score * 0.4 +
            self.keyword_score * 0.3 +
            self.quality_score * 0.2 +
            self.position_score * 0.1
        )


class QueryResult(BaseModel):
    """Complete result for a query"""
    query: Query = Field(..., description="Original query")
    search_results: List[SearchResult] = Field(..., description="Search results")
    ranked_chunks: List[RankedChunk] = Field(..., description="Ranked chunks")
    
    # Processing statistics
    total_chunks_searched: int = Field(..., description="Total chunks searched")
    chunks_above_threshold: int = Field(..., description="Chunks above similarity threshold")
    processing_time_ms: float = Field(..., description="Total processing time")
    
    # Context information
    selected_chunks: List[DocumentChunk] = Field(..., description="Chunks selected for context")
    context_length: int = Field(..., description="Total context length in characters")
    context_truncated: bool = Field(default=False, description="Whether context was truncated")
    
    def get_top_chunks(self, n: int = 5) -> List[DocumentChunk]:
        """Get top N chunks by relevance score"""
        sorted_chunks = sorted(self.ranked_chunks, key=lambda x: x.relevance_score, reverse=True)
        return [rc.chunk for rc in sorted_chunks[:n]]
    
    def get_context_text(self, max_length: int = 4000) -> str:
        """Get concatenated context text with length limit"""
        context_parts = []
        current_length = 0
        
        for chunk in self.selected_chunks:
            chunk_text = chunk.content
            if current_length + len(chunk_text) > max_length:
                # Truncate last chunk if needed
                remaining = max_length - current_length
                if remaining > 100:  # Only add if meaningful length remains
                    context_parts.append(chunk_text[:remaining] + "...")
                    self.context_truncated = True
                break
            
            context_parts.append(chunk_text)
            current_length += len(chunk_text)
        
        return "\n\n".join(context_parts)


class BatchQueryResult(BaseModel):
    """Results for batch query processing"""
    batch_id: str = Field(..., description="Batch identifier")
    query_results: List[QueryResult] = Field(..., description="Individual query results")
    
    # Batch statistics
    total_queries: int = Field(..., description="Total number of queries")
    successful_queries: int = Field(..., description="Successfully processed queries")
    failed_queries: int = Field(..., description="Failed queries")
    total_processing_time_ms: float = Field(..., description="Total batch processing time")
    
    # Resource usage
    total_chunks_processed: int = Field(..., description="Total chunks processed across all queries")
    average_chunks_per_query: float = Field(..., description="Average chunks per query")
    
    def get_successful_results(self) -> List[QueryResult]:
        """Get only successful query results"""
        return [qr for qr in self.query_results if qr.search_results]
