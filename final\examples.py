#!/usr/bin/env python3
"""
Final RAG Model - Usage Examples
Demonstrates various ways to use the optimized RAG system
"""
import asyncio
import json
import time
from typing import List, Dict, Any

# Internal imports
from models.schemas import RAGRequest, ChunkSelectionStrategy
from services.rag_engine import RAGEngine
from config import get_settings
from utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class RAGExamples:
    """Collection of RAG usage examples"""
    
    def __init__(self):
        self.engine = None
    
    async def initialize(self):
        """Initialize the RAG engine"""
        if not self.engine:
            self.engine = RAGEngine()
            await self.engine.initialize()
            logger.info("RAG Engine initialized for examples")
    
    async def example_1_basic_query(self):
        """Example 1: Basic single document query"""
        print("\n" + "="*60)
        print("EXAMPLE 1: Basic Single Document Query")
        print("="*60)
        
        try:
            # Sample document URL (you can replace with any accessible PDF/DOCX)
            document_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
            
            # Simple query
            result = await self.engine.process_single_query(
                query_text="What is this document about?",
                document_source=document_url
            )
            
            print(f"Question: {result['question']}")
            print(f"Answer: {result['answer']}")
            print(f"Confidence: {result['confidence']:.2f}")
            print(f"Processing Time: {result['processing_time_ms']:.2f}ms")
            print(f"Chunks Processed: {result.get('chunks_processed', 'N/A')}")
            
        except Exception as e:
            print(f"Error in example 1: {str(e)}")
    
    async def example_2_multiple_questions(self):
        """Example 2: Multiple questions on one document"""
        print("\n" + "="*60)
        print("EXAMPLE 2: Multiple Questions on One Document")
        print("="*60)
        
        try:
            request = RAGRequest(
                documents="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
                questions=[
                    "What is the main topic of this document?",
                    "What are the key points mentioned?",
                    "Is there any technical information provided?"
                ],
                chunk_strategy=ChunkSelectionStrategy.SMART,
                max_chunks=5,
                similarity_threshold=0.6,
                include_sources=True,
                include_confidence=True,
                include_reasoning=True
            )
            
            response = await self.engine.process_request(request)
            
            print(f"Request ID: {response.request_id}")
            print(f"Status: {response.status}")
            print(f"Total Processing Time: {response.total_processing_time_ms:.2f}ms")
            print(f"Documents Processed: {response.documents_processed}")
            print(f"Total Chunks Created: {response.total_chunks_created}")
            
            for i, answer in enumerate(response.answers, 1):
                print(f"\n--- Answer {i} ---")
                print(f"Question: {answer.question}")
                print(f"Answer: {answer.answer}")
                print(f"Confidence: {answer.confidence.overall:.2f}")
                print(f"Sources: {len(answer.sources)}")
                print(f"Reasoning: {answer.reasoning[:100]}...")
                
        except Exception as e:
            print(f"Error in example 2: {str(e)}")
    
    async def example_3_strategy_comparison(self):
        """Example 3: Compare different chunk selection strategies"""
        print("\n" + "="*60)
        print("EXAMPLE 3: Chunk Selection Strategy Comparison")
        print("="*60)
        
        strategies = [
            ChunkSelectionStrategy.SEMANTIC,
            ChunkSelectionStrategy.KEYWORD,
            ChunkSelectionStrategy.HYBRID,
            ChunkSelectionStrategy.SMART
        ]
        
        question = "What are the main features mentioned?"
        document_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
        results = {}
        
        for strategy in strategies:
            try:
                print(f"\nTesting {strategy.value} strategy...")
                
                request = RAGRequest(
                    documents=document_url,
                    questions=[question],
                    chunk_strategy=strategy,
                    max_chunks=3,
                    similarity_threshold=0.7
                )
                
                start_time = time.time()
                response = await self.engine.process_request(request)
                processing_time = (time.time() - start_time) * 1000
                
                if response.answers:
                    answer = response.answers[0]
                    results[strategy.value] = {
                        "answer": answer.answer[:100] + "...",
                        "confidence": answer.confidence.overall,
                        "chunks_processed": answer.chunks_processed,
                        "processing_time_ms": processing_time
                    }
                
            except Exception as e:
                print(f"Error with {strategy.value}: {str(e)}")
                results[strategy.value] = {"error": str(e)}
        
        # Display comparison
        print("\n--- Strategy Comparison Results ---")
        for strategy, result in results.items():
            print(f"\n{strategy.upper()}:")
            if "error" in result:
                print(f"  Error: {result['error']}")
            else:
                print(f"  Confidence: {result['confidence']:.2f}")
                print(f"  Chunks: {result['chunks_processed']}")
                print(f"  Time: {result['processing_time_ms']:.2f}ms")
                print(f"  Answer: {result['answer']}")
    
    async def example_4_performance_optimization(self):
        """Example 4: Demonstrate performance optimization"""
        print("\n" + "="*60)
        print("EXAMPLE 4: Performance Optimization Demo")
        print("="*60)
        
        try:
            # Get initial stats
            initial_stats = self.engine.get_system_stats()
            print("Initial System Stats:")
            print(f"  Cache entries: {initial_stats.get('embedding_cache_stats', {}).get('cache_entries', 0)}")
            
            # Process same query multiple times to show caching effect
            query = "What is the document structure?"
            document_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
            
            times = []
            
            for i in range(3):
                print(f"\nRun {i+1}:")
                start_time = time.time()
                
                result = await self.engine.process_single_query(query, document_url)
                
                processing_time = (time.time() - start_time) * 1000
                times.append(processing_time)
                
                print(f"  Processing time: {processing_time:.2f}ms")
                print(f"  Answer length: {len(result['answer'])} chars")
            
            # Show performance improvement
            print(f"\nPerformance Analysis:")
            print(f"  First run: {times[0]:.2f}ms (cold start)")
            if len(times) > 1:
                print(f"  Second run: {times[1]:.2f}ms (cached)")
                improvement = ((times[0] - times[1]) / times[0]) * 100
                print(f"  Improvement: {improvement:.1f}%")
            
            # Final stats
            final_stats = self.engine.get_system_stats()
            print(f"\nFinal cache entries: {final_stats.get('embedding_cache_stats', {}).get('cache_entries', 0)}")
            
        except Exception as e:
            print(f"Error in example 4: {str(e)}")
    
    async def example_5_batch_processing(self):
        """Example 5: Batch processing multiple documents"""
        print("\n" + "="*60)
        print("EXAMPLE 5: Batch Processing (Simulated)")
        print("="*60)
        
        try:
            # Simulate batch processing with multiple queries on same document
            questions = [
                "What is the document format?",
                "What is the file size?",
                "What encoding is used?",
                "Are there any special features?",
                "What is the document purpose?"
            ]
            
            document_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
            
            print(f"Processing {len(questions)} questions in batch...")
            
            request = RAGRequest(
                documents=document_url,
                questions=questions,
                chunk_strategy=ChunkSelectionStrategy.SMART,
                max_chunks=4,
                similarity_threshold=0.6
            )
            
            start_time = time.time()
            response = await self.engine.process_request(request)
            total_time = (time.time() - start_time) * 1000
            
            print(f"\nBatch Results:")
            print(f"  Total questions: {len(questions)}")
            print(f"  Successful answers: {len(response.answers)}")
            print(f"  Total time: {total_time:.2f}ms")
            print(f"  Average per question: {total_time/len(questions):.2f}ms")
            print(f"  Chunks created: {response.total_chunks_created}")
            
            # Show sample answers
            for i, answer in enumerate(response.answers[:3], 1):
                print(f"\n  Sample Answer {i}:")
                print(f"    Q: {answer.question}")
                print(f"    A: {answer.answer[:80]}...")
                print(f"    Confidence: {answer.confidence.overall:.2f}")
            
        except Exception as e:
            print(f"Error in example 5: {str(e)}")
    
    async def example_6_system_monitoring(self):
        """Example 6: System monitoring and statistics"""
        print("\n" + "="*60)
        print("EXAMPLE 6: System Monitoring and Statistics")
        print("="*60)
        
        try:
            # Get comprehensive system stats
            stats = self.engine.get_system_stats()
            
            print("System Status:")
            print(f"  Engine Status: {stats.get('engine_status', 'unknown')}")
            
            print("\nProcessing Statistics:")
            proc_stats = stats.get('processing_stats', {})
            print(f"  Documents Processed: {proc_stats.get('documents_processed', 0)}")
            print(f"  Chunks Processed: {proc_stats.get('chunks_processed', 0)}")
            print(f"  Total Searches: {proc_stats.get('total_searches', 0)}")
            print(f"  Processing Efficiency: {proc_stats.get('processing_efficiency', 0):.2%}")
            
            print("\nVector Store:")
            vector_stats = stats.get('vector_store_stats', {})
            print(f"  Total Chunks: {vector_stats.get('total_chunks', 0)}")
            print(f"  Index Size: {vector_stats.get('index_size', 0)}")
            print(f"  Dimension: {vector_stats.get('dimension', 0)}")
            
            print("\nCache Statistics:")
            cache_stats = stats.get('embedding_cache_stats', {})
            print(f"  Cache Entries: {cache_stats.get('cache_entries', 0)}")
            print(f"  Cache Size: {cache_stats.get('cache_size_mb', 0):.2f} MB")
            
            print("\nConfiguration:")
            config = stats.get('configuration', {})
            for key, value in config.items():
                print(f"  {key.replace('_', ' ').title()}: {value}")
            
        except Exception as e:
            print(f"Error in example 6: {str(e)}")
    
    async def run_all_examples(self):
        """Run all examples in sequence"""
        print("🚀 Final RAG Model - Usage Examples")
        print("=" * 60)
        
        await self.initialize()
        
        examples = [
            self.example_1_basic_query,
            self.example_2_multiple_questions,
            self.example_3_strategy_comparison,
            self.example_4_performance_optimization,
            self.example_5_batch_processing,
            self.example_6_system_monitoring
        ]
        
        for i, example in enumerate(examples, 1):
            try:
                await example()
                if i < len(examples):
                    print("\n" + "-" * 40)
                    await asyncio.sleep(1)  # Brief pause between examples
            except Exception as e:
                print(f"Failed to run example {i}: {str(e)}")
        
        print("\n" + "=" * 60)
        print("✅ All examples completed!")
        print("=" * 60)


async def main():
    """Main function to run examples"""
    examples = RAGExamples()
    await examples.run_all_examples()


if __name__ == "__main__":
    asyncio.run(main())
