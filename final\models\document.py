"""
Document-related models for the Final RAG Model
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum
import hashlib


class DocumentType(str, Enum):
    """Supported document types"""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    HTML = "html"
    MARKDOWN = "md"


class DocumentMetadata(BaseModel):
    """Metadata for a document"""
    title: Optional[str] = Field(None, description="Document title")
    author: Optional[str] = Field(None, description="Document author")
    creation_date: Optional[datetime] = Field(None, description="Document creation date")
    modification_date: Optional[datetime] = Field(None, description="Last modification date")
    page_count: Optional[int] = Field(None, description="Number of pages")
    word_count: Optional[int] = Field(None, description="Total word count")
    language: Optional[str] = Field(None, description="Document language")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    
    # Custom metadata
    custom_fields: Dict[str, Any] = Field(default_factory=dict, description="Custom metadata fields")


class ChunkMetadata(BaseModel):
    """Metadata for a document chunk"""
    chunk_index: int = Field(..., description="Index of chunk in document")
    start_char: int = Field(..., description="Starting character position")
    end_char: int = Field(..., description="Ending character position")
    page_number: Optional[int] = Field(None, description="Page number if available")
    section_title: Optional[str] = Field(None, description="Section title if available")
    
    # Quality metrics
    word_count: int = Field(..., description="Number of words in chunk")
    sentence_count: int = Field(..., description="Number of sentences in chunk")
    avg_sentence_length: float = Field(..., description="Average sentence length")
    
    # Processing metadata
    processing_timestamp: datetime = Field(default_factory=datetime.utcnow)
    quality_score: Optional[float] = Field(None, description="Quality score (0-1)")


class DocumentChunk(BaseModel):
    """A chunk of text from a document with optimized processing"""
    id: str = Field(..., description="Unique chunk identifier")
    document_id: str = Field(..., description="Parent document identifier")
    content: str = Field(..., description="Chunk text content")
    metadata: ChunkMetadata = Field(..., description="Chunk metadata")
    
    # Embeddings (optional, computed on demand)
    embedding: Optional[List[float]] = Field(None, description="Vector embedding")
    embedding_model: Optional[str] = Field(None, description="Model used for embedding")
    
    # Optimization flags
    is_processed: bool = Field(default=False, description="Whether chunk has been processed")
    is_relevant: Optional[bool] = Field(None, description="Relevance flag for current query")
    relevance_score: Optional[float] = Field(None, description="Relevance score for current query")
    
    @validator('content')
    def validate_content(cls, v):
        """Validate chunk content"""
        if not v or not v.strip():
            raise ValueError("Chunk content cannot be empty")
        if len(v) > 5000:  # Reasonable limit
            raise ValueError("Chunk content too long (max 5000 characters)")
        return v.strip()
    
    @property
    def content_hash(self) -> str:
        """Generate hash of content for deduplication"""
        return hashlib.md5(self.content.encode()).hexdigest()
    
    def calculate_quality_score(self) -> float:
        """Calculate quality score based on content characteristics"""
        content = self.content.strip()
        
        # Basic quality metrics
        word_count = len(content.split())
        sentence_count = content.count('.') + content.count('!') + content.count('?')
        
        # Quality factors
        length_score = min(word_count / 50, 1.0)  # Prefer 50+ words
        sentence_score = min(sentence_count / 3, 1.0)  # Prefer 3+ sentences
        
        # Penalize very short or very long chunks
        if word_count < 10:
            length_score *= 0.5
        elif word_count > 200:
            length_score *= 0.8
        
        # Check for meaningful content (not just headers/numbers)
        meaningful_chars = sum(1 for c in content if c.isalpha())
        meaningful_score = min(meaningful_chars / len(content), 1.0) if content else 0
        
        # Combined score
        quality_score = (length_score * 0.4 + sentence_score * 0.3 + meaningful_score * 0.3)
        
        # Update metadata
        self.metadata.quality_score = quality_score
        
        return quality_score


class Document(BaseModel):
    """A document with optimized chunk processing"""
    id: str = Field(..., description="Unique document identifier")
    url: Optional[str] = Field(None, description="Source URL if applicable")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Full document content")
    document_type: DocumentType = Field(..., description="Document type")
    metadata: DocumentMetadata = Field(..., description="Document metadata")
    
    # Processing status
    processing_status: str = Field(default="pending", description="Processing status")
    processing_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Chunks (computed on demand)
    chunks: List[DocumentChunk] = Field(default_factory=list, description="Document chunks")
    total_chunks: int = Field(default=0, description="Total number of chunks")
    
    # Optimization metadata
    is_indexed: bool = Field(default=False, description="Whether document is indexed")
    index_timestamp: Optional[datetime] = Field(None, description="When document was indexed")
    
    @validator('content')
    def validate_content(cls, v):
        """Validate document content"""
        if not v or not v.strip():
            raise ValueError("Document content cannot be empty")
        return v.strip()
    
    @property
    def content_hash(self) -> str:
        """Generate hash of content for deduplication"""
        return hashlib.md5(self.content.encode()).hexdigest()
    
    def get_chunk_by_id(self, chunk_id: str) -> Optional[DocumentChunk]:
        """Get chunk by ID"""
        for chunk in self.chunks:
            if chunk.id == chunk_id:
                return chunk
        return None
    
    def get_high_quality_chunks(self, min_quality: float = 0.6) -> List[DocumentChunk]:
        """Get chunks above quality threshold"""
        return [
            chunk for chunk in self.chunks 
            if chunk.metadata.quality_score and chunk.metadata.quality_score >= min_quality
        ]
    
    def get_relevant_chunks(self, min_relevance: float = 0.7) -> List[DocumentChunk]:
        """Get chunks above relevance threshold for current query"""
        return [
            chunk for chunk in self.chunks 
            if chunk.relevance_score and chunk.relevance_score >= min_relevance
        ]
    
    def update_processing_status(self, status: str):
        """Update processing status with timestamp"""
        self.processing_status = status
        self.processing_timestamp = datetime.utcnow()
        
        if status == "completed":
            self.is_indexed = True
            self.index_timestamp = datetime.utcnow()


class DocumentCollection(BaseModel):
    """Collection of documents for batch processing"""
    id: str = Field(..., description="Collection identifier")
    name: str = Field(..., description="Collection name")
    documents: List[Document] = Field(default_factory=list, description="Documents in collection")
    
    # Collection metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    total_documents: int = Field(default=0, description="Total number of documents")
    total_chunks: int = Field(default=0, description="Total number of chunks")
    
    def add_document(self, document: Document):
        """Add document to collection"""
        self.documents.append(document)
        self.total_documents = len(self.documents)
        self.total_chunks = sum(doc.total_chunks for doc in self.documents)
        self.updated_at = datetime.utcnow()
    
    def get_all_chunks(self) -> List[DocumentChunk]:
        """Get all chunks from all documents"""
        all_chunks = []
        for doc in self.documents:
            all_chunks.extend(doc.chunks)
        return all_chunks
    
    def get_document_by_id(self, doc_id: str) -> Optional[Document]:
        """Get document by ID"""
        for doc in self.documents:
            if doc.id == doc_id:
                return doc
        return None
