"""
Final RAG Model - FastAPI Application
Optimized RAG system with efficient chunk processing
"""
import asyncio
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Internal imports
from .models.schemas import RAGRequest, RAGResponse, ErrorResponse
from .services.rag_engine import RAGEngine
from .config import get_settings, validate_environment
from .utils.logger import get_logger

# Initialize
logger = get_logger(__name__)
settings = get_settings()

# Global RAG engine instance
rag_engine = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global rag_engine
    
    try:
        # Startup
        logger.info("Starting Final RAG Model API...")
        
        # Validate environment
        validate_environment()
        
        # Initialize RAG engine
        rag_engine = RAGEngine()
        await rag_engine.initialize()
        
        logger.info("Final RAG Model API started successfully")
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down Final RAG Model API...")


# Create FastAPI app
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description="Optimized RAG system with efficient chunk processing for high-performance document Q&A",
    lifespan=lifespan
)

# Add CORS middleware
if settings.enable_cors:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


def get_rag_engine() -> RAGEngine:
    """Dependency to get RAG engine instance"""
    if rag_engine is None:
        raise HTTPException(status_code=503, detail="RAG engine not initialized")
    return rag_engine


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "name": settings.api_title,
        "version": settings.api_version,
        "description": "Optimized RAG system with efficient chunk processing",
        "status": "running",
        "timestamp": time.time(),
        "features": [
            "Smart chunk selection",
            "Optimized embedding caching",
            "Batch processing",
            "Multiple selection strategies",
            "Real-time performance monitoring"
        ]
    }


@app.get("/health")
async def health_check(engine: RAGEngine = Depends(get_rag_engine)):
    """Health check endpoint"""
    try:
        stats = engine.get_system_stats()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "engine_initialized": stats.get("engine_status") == "initialized",
            "vector_store_chunks": stats.get("vector_store_stats", {}).get("total_chunks", 0),
            "cache_stats": {
                "embedding_cache_size": stats.get("embedding_cache_stats", {}).get("cache_entries", 0),
                "search_cache_size": stats.get("search_cache_stats", {}).get("cache_entries", 0)
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.post("/query", response_model=RAGResponse)
async def process_rag_query(
    request: RAGRequest,
    background_tasks: BackgroundTasks,
    engine: RAGEngine = Depends(get_rag_engine)
):
    """
    Process RAG query with document analysis and question answering
    
    This endpoint processes documents and answers questions using optimized
    chunk selection for improved performance and accuracy.
    """
    try:
        logger.info(f"Received RAG request with {len(request.questions)} questions")
        
        # Validate request
        if not request.documents:
            raise HTTPException(status_code=400, detail="No documents provided")
        
        if not request.questions:
            raise HTTPException(status_code=400, detail="No questions provided")
        
        # Process request
        start_time = time.time()
        response = await engine.process_request(request)
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"RAG request processed in {processing_time:.2f}ms")
        
        # Add background task to log statistics
        background_tasks.add_task(log_request_stats, request, response, processing_time)
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing RAG query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/query/single")
async def process_single_query(
    query: str,
    document_url: str = None,
    max_chunks: int = None,
    engine: RAGEngine = Depends(get_rag_engine)
):
    """
    Process a single query (convenience endpoint)
    
    Simplified endpoint for processing a single question, optionally with a document.
    """
    try:
        if not query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        # Use provided max_chunks or default
        if max_chunks is None:
            max_chunks = settings.max_chunks_per_query
        
        # Process single query
        result = await engine.process_single_query(query, document_url)
        
        return {
            "success": True,
            "result": result,
            "timestamp": time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing single query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/stats")
async def get_system_statistics(engine: RAGEngine = Depends(get_rag_engine)):
    """Get comprehensive system statistics"""
    try:
        stats = engine.get_system_stats()
        
        return {
            "success": True,
            "statistics": stats,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Error getting system statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving statistics: {str(e)}")


@app.post("/cache/clear")
async def clear_caches(
    background_tasks: BackgroundTasks,
    engine: RAGEngine = Depends(get_rag_engine)
):
    """Clear all system caches"""
    try:
        # Clear caches in background
        background_tasks.add_task(engine.clear_all_caches)
        
        return {
            "success": True,
            "message": "Cache clearing initiated",
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Error clearing caches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing caches: {str(e)}")


@app.get("/config")
async def get_configuration():
    """Get current system configuration"""
    try:
        from .config import get_config_summary
        
        config_summary = get_config_summary()
        
        return {
            "success": True,
            "configuration": config_summary,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Error getting configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving configuration: {str(e)}")


# Background task functions
async def log_request_stats(request: RAGRequest, response: RAGResponse, processing_time: float):
    """Log request statistics in background"""
    try:
        logger.info(f"Request Stats - Documents: {len(request.documents) if isinstance(request.documents, list) else 1}, "
                   f"Questions: {len(request.questions)}, "
                   f"Processing Time: {processing_time:.2f}ms, "
                   f"Chunks Created: {response.total_chunks_created}, "
                   f"Answers Generated: {len(response.answers)}")
    except Exception as e:
        logger.error(f"Error logging request stats: {str(e)}")


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error_code=f"HTTP_{exc.status_code}",
            error_message=exc.detail,
            details={"path": str(request.url)}
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}")
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error_code="INTERNAL_SERVER_ERROR",
            error_message="An unexpected error occurred",
            details={"path": str(request.url)}
        ).dict()
    )


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level=settings.log_level.lower()
    )
