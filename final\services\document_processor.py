"""
Document Processing Service for Final RAG Model
Optimized for efficient chunk processing and high performance
"""
import asyncio
import aiohttp
import aiofiles
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import hashlib
import re
import uuid
from pathlib import Path

# Document processing libraries
import PyPDF2
from docx import Document as DocxDocument
from io import BytesIO

# Internal imports
from ..models.document import Document, DocumentChunk, DocumentMetadata, ChunkMetadata, DocumentType
from ..config import get_settings, get_chunk_config
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()
chunk_config = get_chunk_config()


class DocumentProcessor:
    """Base document processor with standard functionality"""
    
    def __init__(self):
        self.supported_formats = {
            'pdf': self._process_pdf,
            'docx': self._process_docx,
            'txt': self._process_txt,
            'html': self._process_html,
            'md': self._process_markdown
        }
    
    async def process_document(self, source: Union[str, bytes], document_type: str = None) -> Document:
        """
        Process a document from URL or content
        
        Args:
            source: URL string or document bytes
            document_type: Document type (auto-detected if None)
            
        Returns:
            Processed Document object
        """
        try:
            logger.info(f"Processing document: {type(source).__name__}")
            
            # Download if URL
            if isinstance(source, str) and source.startswith(('http://', 'https://')):
                content, detected_type = await self._download_document(source)
                url = source
            else:
                content = source if isinstance(source, bytes) else source.encode()
                detected_type = document_type
                url = None
            
            # Auto-detect type if not provided
            if not detected_type:
                detected_type = self._detect_document_type(content)
            
            # Process based on type
            if detected_type not in self.supported_formats:
                raise ValueError(f"Unsupported document type: {detected_type}")
            
            text_content, metadata = await self.supported_formats[detected_type](content)
            
            # Create document
            doc_id = str(uuid.uuid4())
            document = Document(
                id=doc_id,
                url=url,
                title=metadata.get('title', f'Document_{doc_id[:8]}'),
                content=text_content,
                document_type=DocumentType(detected_type),
                metadata=DocumentMetadata(**metadata),
                processing_status="completed"
            )
            
            logger.info(f"Document processed successfully: {len(text_content)} characters")
            return document
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            raise
    
    async def _download_document(self, url: str) -> tuple[bytes, str]:
        """Download document from URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise ValueError(f"Failed to download document: HTTP {response.status}")
                    
                    content = await response.read()
                    
                    # Detect type from content-type header
                    content_type = response.headers.get('content-type', '').lower()
                    if 'pdf' in content_type:
                        doc_type = 'pdf'
                    elif 'word' in content_type or 'docx' in content_type:
                        doc_type = 'docx'
                    elif 'text' in content_type:
                        doc_type = 'txt'
                    else:
                        doc_type = self._detect_document_type(content)
                    
                    return content, doc_type
                    
        except Exception as e:
            logger.error(f"Error downloading document from {url}: {str(e)}")
            raise
    
    def _detect_document_type(self, content: bytes) -> str:
        """Detect document type from content"""
        # PDF signature
        if content.startswith(b'%PDF'):
            return 'pdf'
        
        # DOCX signature (ZIP with specific structure)
        if content.startswith(b'PK') and b'word/' in content:
            return 'docx'
        
        # Try to decode as text
        try:
            content.decode('utf-8')
            return 'txt'
        except UnicodeDecodeError:
            pass
        
        # Default fallback
        return 'txt'
    
    async def _process_pdf(self, content: bytes) -> tuple[str, dict]:
        """Process PDF document"""
        try:
            pdf_file = BytesIO(content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"
            
            metadata = {
                'page_count': len(pdf_reader.pages),
                'word_count': len(text_content.split()),
                'file_size': len(content)
            }
            
            # Extract PDF metadata if available
            if pdf_reader.metadata:
                metadata.update({
                    'title': pdf_reader.metadata.get('/Title', ''),
                    'author': pdf_reader.metadata.get('/Author', ''),
                    'creation_date': pdf_reader.metadata.get('/CreationDate'),
                })
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            raise
    
    async def _process_docx(self, content: bytes) -> tuple[str, dict]:
        """Process DOCX document"""
        try:
            docx_file = BytesIO(content)
            doc = DocxDocument(docx_file)
            
            text_content = ""
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            metadata = {
                'word_count': len(text_content.split()),
                'file_size': len(content)
            }
            
            # Extract DOCX metadata
            core_props = doc.core_properties
            if core_props:
                metadata.update({
                    'title': core_props.title or '',
                    'author': core_props.author or '',
                    'creation_date': core_props.created,
                    'modification_date': core_props.modified
                })
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing DOCX: {str(e)}")
            raise
    
    async def _process_txt(self, content: bytes) -> tuple[str, dict]:
        """Process text document"""
        try:
            text_content = content.decode('utf-8')
            
            metadata = {
                'word_count': len(text_content.split()),
                'file_size': len(content),
                'language': 'en'  # Could be enhanced with language detection
            }
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing text: {str(e)}")
            raise
    
    async def _process_html(self, content: bytes) -> tuple[str, dict]:
        """Process HTML document (basic implementation)"""
        try:
            # Simple HTML tag removal (could be enhanced with BeautifulSoup)
            html_content = content.decode('utf-8')
            text_content = re.sub(r'<[^>]+>', '', html_content)
            text_content = re.sub(r'\s+', ' ', text_content)
            
            metadata = {
                'word_count': len(text_content.split()),
                'file_size': len(content)
            }
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing HTML: {str(e)}")
            raise
    
    async def _process_markdown(self, content: bytes) -> tuple[str, dict]:
        """Process Markdown document"""
        try:
            # Basic markdown processing (could be enhanced with markdown library)
            md_content = content.decode('utf-8')
            
            # Remove markdown syntax (basic)
            text_content = re.sub(r'#{1,6}\s+', '', md_content)  # Headers
            text_content = re.sub(r'\*\*(.*?)\*\*', r'\1', text_content)  # Bold
            text_content = re.sub(r'\*(.*?)\*', r'\1', text_content)  # Italic
            text_content = re.sub(r'`(.*?)`', r'\1', text_content)  # Code
            
            metadata = {
                'word_count': len(text_content.split()),
                'file_size': len(content)
            }
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing Markdown: {str(e)}")
            raise


class SmartDocumentProcessor(DocumentProcessor):
    """Enhanced document processor with smart chunking and optimization"""
    
    def __init__(self):
        super().__init__()
        self.chunk_cache = {}  # Cache for processed chunks
    
    async def process_document_with_chunks(self, source: Union[str, bytes], 
                                         document_type: str = None) -> Document:
        """
        Process document and create optimized chunks
        
        Args:
            source: URL string or document bytes
            document_type: Document type (auto-detected if None)
            
        Returns:
            Document with optimized chunks
        """
        try:
            # Process base document
            document = await self.process_document(source, document_type)
            
            # Create optimized chunks
            chunks = await self.create_smart_chunks(document)
            document.chunks = chunks
            document.total_chunks = len(chunks)
            
            logger.info(f"Created {len(chunks)} optimized chunks for document {document.id}")
            return document
            
        except Exception as e:
            logger.error(f"Error processing document with chunks: {str(e)}")
            raise
    
    async def create_smart_chunks(self, document: Document) -> List[DocumentChunk]:
        """Create optimized chunks with quality filtering"""
        try:
            text = document.content
            chunks = []
            
            # Split into sentences first
            sentences = self._split_into_sentences(text)
            
            # Group sentences into chunks
            current_chunk = ""
            current_sentences = []
            chunk_index = 0
            
            for sentence in sentences:
                # Check if adding this sentence would exceed chunk size
                potential_chunk = current_chunk + " " + sentence if current_chunk else sentence
                
                if len(potential_chunk) > settings.chunk_size and current_chunk:
                    # Create chunk from current content
                    chunk = await self._create_chunk(
                        document.id, 
                        current_chunk.strip(), 
                        chunk_index,
                        current_sentences
                    )
                    
                    if chunk and self._is_quality_chunk(chunk):
                        chunks.append(chunk)
                        chunk_index += 1
                    
                    # Start new chunk with overlap
                    overlap_sentences = current_sentences[-2:] if len(current_sentences) > 2 else []
                    current_chunk = " ".join([s for s in overlap_sentences]) + " " + sentence
                    current_sentences = overlap_sentences + [sentence]
                else:
                    current_chunk = potential_chunk
                    current_sentences.append(sentence)
            
            # Handle remaining content
            if current_chunk.strip():
                chunk = await self._create_chunk(
                    document.id, 
                    current_chunk.strip(), 
                    chunk_index,
                    current_sentences
                )
                if chunk and self._is_quality_chunk(chunk):
                    chunks.append(chunk)
            
            logger.info(f"Created {len(chunks)} quality chunks from {len(sentences)} sentences")
            return chunks
            
        except Exception as e:
            logger.error(f"Error creating smart chunks: {str(e)}")
            raise
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences with smart handling"""
        # Enhanced sentence splitting
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        # Filter out very short sentences and clean up
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10 and not sentence.isdigit():  # Skip very short or numeric-only
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    async def _create_chunk(self, document_id: str, content: str, 
                          index: int, sentences: List[str]) -> Optional[DocumentChunk]:
        """Create a document chunk with metadata"""
        try:
            chunk_id = f"{document_id}_chunk_{index}"
            
            # Calculate metadata
            word_count = len(content.split())
            sentence_count = len(sentences)
            avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
            
            metadata = ChunkMetadata(
                chunk_index=index,
                start_char=0,  # Could be enhanced to track actual positions
                end_char=len(content),
                word_count=word_count,
                sentence_count=sentence_count,
                avg_sentence_length=avg_sentence_length
            )
            
            chunk = DocumentChunk(
                id=chunk_id,
                document_id=document_id,
                content=content,
                metadata=metadata
            )
            
            # Calculate quality score
            chunk.calculate_quality_score()
            
            return chunk
            
        except Exception as e:
            logger.error(f"Error creating chunk: {str(e)}")
            return None
    
    def _is_quality_chunk(self, chunk: DocumentChunk) -> bool:
        """Determine if chunk meets quality standards"""
        if not chunk.metadata.quality_score:
            return False
        
        # Quality thresholds
        min_quality = chunk_config.min_chunk_length / 100  # Convert to 0-1 scale
        min_words = chunk_config.min_word_count
        
        return (
            chunk.metadata.quality_score >= min_quality and
            chunk.metadata.word_count >= min_words and
            len(chunk.content.strip()) >= chunk_config.min_chunk_length
        )
