"""
Configuration settings for the Final RAG Model
Optimized for efficient chunk processing and high performance
"""
import os
from typing import Optional, List
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class RAGConfig(BaseSettings):
    """Configuration for the RAG system with optimized chunk processing"""
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_title: str = Field(default="Final RAG Model API", env="API_TITLE")
    api_version: str = Field(default="1.0.0", env="API_VERSION")
    
    # Gemini AI Configuration
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    gemini_model: str = Field(default="gemini-1.5-flash", env="GEMINI_MODEL")
    gemini_embedding_model: str = Field(default="models/embedding-001", env="GEMINI_EMBEDDING_MODEL")
    gemini_temperature: float = Field(default=0.1, env="GEMINI_TEMPERATURE")
    gemini_max_tokens: int = Field(default=2048, env="GEMINI_MAX_TOKENS")
    
    # Vector Database Configuration
    vector_dimension: int = Field(default=768, env="VECTOR_DIMENSION")
    faiss_index_path: str = Field(default="./final/data/faiss_index", env="FAISS_INDEX_PATH")
    
    # Optimized Chunk Processing Configuration
    chunk_size: int = Field(default=512, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=64, env="CHUNK_OVERLAP")
    max_chunks_per_query: int = Field(default=5, env="MAX_CHUNKS_PER_QUERY")
    similarity_threshold: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    
    # Smart Chunk Selection Parameters
    enable_smart_chunking: bool = Field(default=True, env="ENABLE_SMART_CHUNKING")
    chunk_relevance_threshold: float = Field(default=0.6, env="CHUNK_RELEVANCE_THRESHOLD")
    max_context_length: int = Field(default=4000, env="MAX_CONTEXT_LENGTH")
    enable_chunk_reranking: bool = Field(default=True, env="ENABLE_CHUNK_RERANKING")
    
    # Performance Optimization
    batch_size: int = Field(default=32, env="BATCH_SIZE")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    cache_embeddings: bool = Field(default=True, env="CACHE_EMBEDDINGS")
    enable_async_processing: bool = Field(default=True, env="ENABLE_ASYNC_PROCESSING")
    
    # Document Processing Configuration
    supported_formats: List[str] = Field(default=["pdf", "docx", "txt"], env="SUPPORTED_FORMATS")
    max_file_size_mb: int = Field(default=50, env="MAX_FILE_SIZE_MB")
    extract_metadata: bool = Field(default=True, env="EXTRACT_METADATA")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./final/logs/rag_model.log", env="LOG_FILE")
    enable_detailed_logging: bool = Field(default=True, env="ENABLE_DETAILED_LOGGING")
    
    # Security Configuration
    enable_cors: bool = Field(default=True, env="ENABLE_CORS")
    allowed_origins: List[str] = Field(default=["*"], env="ALLOWED_ORIGINS")
    api_key_header: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    
    # Response Configuration
    include_sources: bool = Field(default=True, env="INCLUDE_SOURCES")
    include_confidence: bool = Field(default=True, env="INCLUDE_CONFIDENCE")
    include_reasoning: bool = Field(default=True, env="INCLUDE_REASONING")
    max_response_length: int = Field(default=2000, env="MAX_RESPONSE_LENGTH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class ChunkProcessingConfig(BaseModel):
    """Configuration for optimized chunk processing"""
    
    # Chunk Selection Strategy
    selection_strategy: str = Field(default="hybrid", description="Strategy: 'semantic', 'keyword', 'hybrid'")
    
    # Semantic Search Parameters
    semantic_weight: float = Field(default=0.7, description="Weight for semantic similarity")
    keyword_weight: float = Field(default=0.3, description="Weight for keyword matching")
    
    # Chunk Filtering
    min_chunk_length: int = Field(default=50, description="Minimum chunk length in characters")
    max_chunk_length: int = Field(default=2000, description="Maximum chunk length in characters")
    
    # Context Window Optimization
    context_window_size: int = Field(default=4000, description="Maximum context window size")
    overlap_penalty: float = Field(default=0.1, description="Penalty for overlapping content")
    
    # Performance Tuning
    enable_parallel_processing: bool = Field(default=True, description="Enable parallel chunk processing")
    chunk_cache_size: int = Field(default=1000, description="Number of chunks to cache")
    
    # Quality Filters
    enable_quality_filtering: bool = Field(default=True, description="Filter low-quality chunks")
    min_word_count: int = Field(default=10, description="Minimum word count per chunk")
    exclude_headers_only: bool = Field(default=True, description="Exclude chunks with only headers")


# Global configuration instance
settings = RAGConfig()
chunk_config = ChunkProcessingConfig()


def get_settings() -> RAGConfig:
    """Get the global settings instance"""
    return settings


def get_chunk_config() -> ChunkProcessingConfig:
    """Get the chunk processing configuration"""
    return chunk_config


def update_settings(**kwargs) -> RAGConfig:
    """Update settings dynamically"""
    global settings
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)
    return settings


# Environment validation
def validate_environment():
    """Validate required environment variables"""
    required_vars = ["GEMINI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True


# Configuration summary
def get_config_summary() -> dict:
    """Get a summary of current configuration"""
    return {
        "model": settings.gemini_model,
        "chunk_size": settings.chunk_size,
        "max_chunks": settings.max_chunks_per_query,
        "smart_chunking": settings.enable_smart_chunking,
        "similarity_threshold": settings.similarity_threshold,
        "batch_size": settings.batch_size,
        "cache_enabled": settings.cache_embeddings,
        "async_enabled": settings.enable_async_processing
    }
