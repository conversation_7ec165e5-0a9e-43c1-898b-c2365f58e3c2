"""
Embedding Service for Final RAG Model
Optimized for efficient embedding generation and caching
"""
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Union
import hashlib
import pickle
from pathlib import Path
import time

# AI libraries
import google.generativeai as genai
from sentence_transformers import SentenceTransformer

# Internal imports
from ..models.response import EmbeddingResult
from ..config import get_settings
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class EmbeddingService:
    """Base embedding service with Gemini and fallback support"""
    
    def __init__(self):
        self.provider = "gemini"
        self.model_name = settings.gemini_embedding_model
        self.dimension = settings.vector_dimension
        self.fallback_model = None
        self.cache = {}
        self.cache_enabled = settings.cache_embeddings
        
    async def initialize(self):
        """Initialize the embedding service"""
        try:
            logger.info("Initializing embedding service...")
            
            # Configure Gemini
            genai.configure(api_key=settings.gemini_api_key)
            
            # Test Gemini connection
            try:
                test_result = genai.embed_content(
                    model=self.model_name,
                    content="test",
                    task_type="retrieval_document"
                )
                self.dimension = len(test_result['embedding'])
                logger.info(f"Gemini embedding service initialized (dimension: {self.dimension})")
                
            except Exception as e:
                logger.warning(f"Gemini initialization failed: {str(e)}, falling back to sentence-transformers")
                self.provider = "sentence-transformers"
                self.fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.dimension = self.fallback_model.get_sentence_embedding_dimension()
                
        except Exception as e:
            logger.error(f"Error initializing embedding service: {str(e)}")
            raise
    
    async def encode(self, texts: Union[str, List[str]], 
                    task_type: str = "retrieval_document") -> np.ndarray:
        """
        Generate embeddings for texts
        
        Args:
            texts: Single text or list of texts
            task_type: Task type for Gemini ("retrieval_document" or "retrieval_query")
            
        Returns:
            Numpy array of embeddings
        """
        try:
            if isinstance(texts, str):
                texts = [texts]
            
            # Check cache first
            if self.cache_enabled:
                cached_embeddings = self._get_cached_embeddings(texts, task_type)
                if cached_embeddings is not None:
                    return cached_embeddings
            
            start_time = time.time()
            
            if self.provider == "gemini":
                embeddings = await self._encode_with_gemini(texts, task_type)
            else:
                embeddings = await self._encode_with_sentence_transformers(texts)
            
            processing_time = (time.time() - start_time) * 1000
            
            # Cache results
            if self.cache_enabled:
                self._cache_embeddings(texts, embeddings, task_type)
            
            logger.debug(f"Generated embeddings for {len(texts)} texts in {processing_time:.2f}ms")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    async def _encode_with_gemini(self, texts: List[str], task_type: str) -> np.ndarray:
        """Generate embeddings using Gemini"""
        embeddings = []
        
        for text in texts:
            try:
                result = genai.embed_content(
                    model=self.model_name,
                    content=text,
                    task_type=task_type
                )
                embedding = np.array(result['embedding'], dtype=np.float32)
                embeddings.append(embedding)
                
            except Exception as e:
                logger.error(f"Error with Gemini embedding: {str(e)}")
                # Fallback to sentence transformers for this text
                if not self.fallback_model:
                    self.fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
                
                fallback_embedding = self.fallback_model.encode([text])[0]
                embeddings.append(fallback_embedding.astype(np.float32))
        
        embeddings_array = np.vstack(embeddings)
        
        # Normalize for cosine similarity
        norms = np.linalg.norm(embeddings_array, axis=1, keepdims=True)
        embeddings_array = embeddings_array / (norms + 1e-8)
        
        return embeddings_array
    
    async def _encode_with_sentence_transformers(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings using sentence transformers"""
        if not self.fallback_model:
            self.fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        embeddings = self.fallback_model.encode(texts, normalize_embeddings=True)
        return embeddings.astype(np.float32)
    
    def _get_cache_key(self, text: str, task_type: str) -> str:
        """Generate cache key for text and task type"""
        content = f"{text}_{task_type}_{self.provider}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_embeddings(self, texts: List[str], task_type: str) -> Optional[np.ndarray]:
        """Get cached embeddings if available"""
        cached_embeddings = []
        
        for text in texts:
            cache_key = self._get_cache_key(text, task_type)
            if cache_key in self.cache:
                cached_embeddings.append(self.cache[cache_key])
            else:
                return None  # If any text is not cached, return None
        
        if cached_embeddings:
            return np.vstack(cached_embeddings)
        return None
    
    def _cache_embeddings(self, texts: List[str], embeddings: np.ndarray, task_type: str):
        """Cache embeddings for future use"""
        for i, text in enumerate(texts):
            cache_key = self._get_cache_key(text, task_type)
            self.cache[cache_key] = embeddings[i]
    
    async def encode_query(self, query: str) -> np.ndarray:
        """Generate embedding for a query (optimized for search)"""
        return await self.encode(query, task_type="retrieval_query")
    
    async def encode_documents(self, documents: List[str]) -> np.ndarray:
        """Generate embeddings for documents (optimized for indexing)"""
        return await self.encode(documents, task_type="retrieval_document")
    
    def get_dimension(self) -> int:
        """Get embedding dimension"""
        return self.dimension
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information"""
        return {
            "provider": self.provider,
            "model": self.model_name if self.provider == "gemini" else "all-MiniLM-L6-v2",
            "dimension": self.dimension,
            "cache_enabled": self.cache_enabled,
            "cache_size": len(self.cache)
        }


class OptimizedEmbeddingService(EmbeddingService):
    """Optimized embedding service with batch processing and smart caching"""

    def __init__(self):
        super().__init__()
        self.batch_size = settings.batch_size
        self.persistent_cache_path = Path(settings.faiss_index_path).parent / "embedding_cache.pkl"
        self.load_persistent_cache()

    def load_persistent_cache(self):
        """Load persistent cache from disk"""
        try:
            if self.persistent_cache_path.exists():
                with open(self.persistent_cache_path, 'rb') as f:
                    self.cache = pickle.load(f)
                logger.info(f"Loaded {len(self.cache)} cached embeddings from disk")
        except Exception as e:
            logger.warning(f"Could not load persistent cache: {str(e)}")
            self.cache = {}

    def save_persistent_cache(self):
        """Save cache to disk"""
        try:
            self.persistent_cache_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.persistent_cache_path, 'wb') as f:
                pickle.dump(self.cache, f)
            logger.debug(f"Saved {len(self.cache)} embeddings to persistent cache")
        except Exception as e:
            logger.warning(f"Could not save persistent cache: {str(e)}")

    async def encode_batch(self, texts: List[str],
                          task_type: str = "retrieval_document") -> np.ndarray:
        """
        Encode texts in batches for better performance

        Args:
            texts: List of texts to encode
            task_type: Task type for Gemini

        Returns:
            Numpy array of embeddings
        """
        try:
            if not texts:
                return np.array([])

            # Check cache for all texts first
            cached_results = {}
            uncached_texts = []

            for i, text in enumerate(texts):
                cache_key = self._get_cache_key(text, task_type)
                if cache_key in self.cache:
                    cached_results[i] = self.cache[cache_key]
                else:
                    uncached_texts.append((i, text))

            # Process uncached texts in batches
            all_embeddings = [None] * len(texts)

            # Fill in cached results
            for i, embedding in cached_results.items():
                all_embeddings[i] = embedding

            # Process uncached texts
            if uncached_texts:
                logger.info(f"Processing {len(uncached_texts)} uncached texts in batches of {self.batch_size}")

                for batch_start in range(0, len(uncached_texts), self.batch_size):
                    batch_end = min(batch_start + self.batch_size, len(uncached_texts))
                    batch_items = uncached_texts[batch_start:batch_end]

                    batch_texts = [item[1] for item in batch_items]
                    batch_embeddings = await self.encode(batch_texts, task_type)

                    # Store results
                    for j, (original_index, text) in enumerate(batch_items):
                        all_embeddings[original_index] = batch_embeddings[j]

                        # Cache the result
                        cache_key = self._get_cache_key(text, task_type)
                        self.cache[cache_key] = batch_embeddings[j]

                # Save cache periodically
                if len(uncached_texts) > 10:
                    self.save_persistent_cache()

            # Convert to numpy array
            result = np.vstack(all_embeddings)

            logger.info(f"Encoded {len(texts)} texts ({len(cached_results)} from cache, "
                       f"{len(uncached_texts)} newly processed)")

            return result

        except Exception as e:
            logger.error(f"Error in batch encoding: {str(e)}")
            raise

    async def encode_with_quality_filter(self, texts: List[str],
                                       min_length: int = 10) -> tuple[np.ndarray, List[int]]:
        """
        Encode texts with quality filtering

        Args:
            texts: List of texts to encode
            min_length: Minimum text length to process

        Returns:
            Tuple of (embeddings, valid_indices)
        """
        try:
            # Filter texts by quality
            valid_texts = []
            valid_indices = []

            for i, text in enumerate(texts):
                if len(text.strip()) >= min_length and text.strip():
                    valid_texts.append(text.strip())
                    valid_indices.append(i)

            if not valid_texts:
                logger.warning("No valid texts found after quality filtering")
                return np.array([]), []

            # Encode valid texts
            embeddings = await self.encode_batch(valid_texts)

            logger.info(f"Quality filtered: {len(valid_texts)}/{len(texts)} texts passed")
            return embeddings, valid_indices

        except Exception as e:
            logger.error(f"Error in quality filtering: {str(e)}")
            raise

    async def encode_with_deduplication(self, texts: List[str]) -> tuple[np.ndarray, List[int]]:
        """
        Encode texts with deduplication

        Args:
            texts: List of texts to encode

        Returns:
            Tuple of (embeddings, unique_indices)
        """
        try:
            # Find unique texts
            seen_texts = {}
            unique_texts = []
            unique_indices = []

            for i, text in enumerate(texts):
                text_hash = hashlib.md5(text.encode()).hexdigest()
                if text_hash not in seen_texts:
                    seen_texts[text_hash] = len(unique_texts)
                    unique_texts.append(text)
                    unique_indices.append(i)

            # Encode unique texts
            embeddings = await self.encode_batch(unique_texts)

            logger.info(f"Deduplicated: {len(unique_texts)}/{len(texts)} unique texts")
            return embeddings, unique_indices

        except Exception as e:
            logger.error(f"Error in deduplication: {str(e)}")
            raise

    def clear_cache(self):
        """Clear embedding cache"""
        self.cache.clear()
        if self.persistent_cache_path.exists():
            self.persistent_cache_path.unlink()
        logger.info("Embedding cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        cache_size_mb = len(pickle.dumps(self.cache)) / (1024 * 1024) if self.cache else 0

        return {
            "cache_entries": len(self.cache),
            "cache_size_mb": round(cache_size_mb, 2),
            "persistent_cache_exists": self.persistent_cache_path.exists(),
            "batch_size": self.batch_size
        }
