"""
Query Processing Service for Final RAG Model
Smart query processing with optimized answer generation
"""
import asyncio
import time
from typing import List, Dict, Any, Optional
import google.generativeai as genai

# Internal imports
from ..models.query import Query, QueryResult, SearchResult
from ..models.document import DocumentChunk
from ..models.response import AnswerGeneration, GenerationMetrics
from ..models.schemas import AnswerWithExplanation, ConfidenceScore, SourceReference
from ..services.vector_store import OptimizedVectorStore
from ..services.chunk_optimizer import SmartChunkSelector
from ..config import get_settings
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class QueryProcessor:
    """Base query processor with LLM integration"""
    
    def __init__(self, vector_store: OptimizedVectorStore = None):
        self.vector_store = vector_store or OptimizedVectorStore()
        self.chunk_selector = SmartChunkSelector()
        
        # Configure Gemini
        genai.configure(api_key=settings.gemini_api_key)
        self.model = genai.GenerativeModel(settings.gemini_model)
        
    async def initialize(self):
        """Initialize the query processor"""
        try:
            logger.info("Initializing query processor...")
            await self.vector_store.initialize()
            logger.info("Query processor initialized")
        except Exception as e:
            logger.error(f"Error initializing query processor: {str(e)}")
            raise
    
    async def process_query(self, query: Query, 
                          document_chunks: List[DocumentChunk] = None) -> AnswerWithExplanation:
        """
        Process a single query and generate answer
        
        Args:
            query: Query to process
            document_chunks: Optional chunks to search in
            
        Returns:
            Answer with explanation
        """
        try:
            logger.info(f"Processing query: {query.text[:100]}...")
            start_time = time.time()
            
            # Analyze query
            query.extract_keywords()
            query.analyze_complexity()
            query.detect_query_type()
            
            # Get relevant context
            if document_chunks:
                # Use provided chunks
                selected_chunks = await self.chunk_selector.select_relevant_chunks(
                    document_chunks, query, settings.max_chunks_per_query
                )
                context = "\n\n".join([chunk.content for chunk in selected_chunks])
            else:
                # Search vector store
                search_results = await self.vector_store.search_with_reranking(
                    query.text, top_k=settings.max_chunks_per_query
                )
                selected_chunks = [result.chunk for result in search_results]
                context = "\n\n".join([result.chunk.content for result in search_results])
            
            # Generate answer
            answer_generation = await self._generate_answer(query, context, selected_chunks)
            
            # Calculate confidence
            confidence = self._calculate_confidence(selected_chunks, query, answer_generation.answer_text)
            
            # Create source references
            sources = self._create_source_references(selected_chunks, query.text)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(selected_chunks, query, answer_generation.answer_text)
            
            processing_time = (time.time() - start_time) * 1000
            
            return AnswerWithExplanation(
                question=query.text,
                answer=answer_generation.answer_text,
                processing_time_ms=processing_time,
                confidence=confidence,
                sources=sources,
                reasoning=reasoning,
                chunks_processed=len(selected_chunks),
                total_chunks_available=len(document_chunks) if document_chunks else len(self.vector_store.chunks)
            )
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            raise
    
    async def _generate_answer(self, query: Query, context: str, 
                             chunks: List[DocumentChunk]) -> AnswerGeneration:
        """Generate answer using LLM"""
        try:
            start_time = time.time()
            
            # Create prompt
            prompt = self._create_prompt(query, context)
            
            # Generate response
            response = await self.model.generate_content_async(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=settings.gemini_temperature,
                    max_output_tokens=settings.gemini_max_tokens,
                )
            )
            
            answer_text = response.text.strip()
            
            # Calculate metrics
            prompt_tokens = len(prompt.split())  # Rough estimate
            completion_tokens = len(answer_text.split())  # Rough estimate
            generation_time = (time.time() - start_time) * 1000
            
            metrics = GenerationMetrics(
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=prompt_tokens + completion_tokens,
                prompt_processing_time_ms=generation_time * 0.1,  # Estimate
                generation_time_ms=generation_time * 0.9,  # Estimate
                total_time_ms=generation_time,
                response_length=len(answer_text)
            )
            
            return AnswerGeneration(
                query_id=query.id,
                answer_text=answer_text,
                context_chunks=[chunk.id for chunk in chunks],
                context_length=len(context),
                model_used=settings.gemini_model,
                temperature=settings.gemini_temperature,
                max_tokens=settings.gemini_max_tokens,
                metrics=metrics,
                confidence_score=0.8  # Default confidence
            )
            
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}")
            raise
    
    def _create_prompt(self, query: Query, context: str) -> str:
        """Create prompt for LLM"""
        prompt_template = """
You are an expert assistant that answers questions based on provided context.

Context:
{context}

Question: {question}

Instructions:
1. Answer the question based ONLY on the provided context
2. If the context doesn't contain enough information, say so clearly
3. Be concise but comprehensive
4. Use specific details from the context when possible
5. If there are multiple relevant pieces of information, synthesize them

Answer:
"""
        
        return prompt_template.format(
            context=context[:settings.max_context_length],
            question=query.text
        )
    
    def _calculate_confidence(self, chunks: List[DocumentChunk], 
                            query: Query, answer: str) -> ConfidenceScore:
        """Calculate confidence score for the answer"""
        try:
            # Semantic similarity (based on chunk relevance)
            if chunks:
                avg_quality = sum(chunk.metadata.quality_score or 0.5 for chunk in chunks) / len(chunks)
                semantic_similarity = min(avg_quality * 1.2, 1.0)
            else:
                semantic_similarity = 0.3
            
            # Keyword match (check if query keywords appear in answer)
            query_keywords = set(query.keywords) if query.keywords else set()
            answer_words = set(answer.lower().split())
            
            if query_keywords:
                keyword_matches = len(query_keywords.intersection(answer_words))
                keyword_match = keyword_matches / len(query_keywords)
            else:
                keyword_match = 0.5
            
            # Context relevance (based on answer length and detail)
            if len(answer) > 50 and "don't know" not in answer.lower():
                context_relevance = min(len(answer) / 200, 1.0)
            else:
                context_relevance = 0.3
            
            # Overall confidence
            overall = (semantic_similarity * 0.4 + keyword_match * 0.3 + context_relevance * 0.3)
            
            return ConfidenceScore(
                overall=overall,
                semantic_similarity=semantic_similarity,
                keyword_match=keyword_match,
                context_relevance=context_relevance
            )
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {str(e)}")
            return ConfidenceScore(
                overall=0.5,
                semantic_similarity=0.5,
                keyword_match=0.5,
                context_relevance=0.5
            )
    
    def _create_source_references(self, chunks: List[DocumentChunk], 
                                query: str) -> List[SourceReference]:
        """Create source references from chunks"""
        sources = []
        
        for i, chunk in enumerate(chunks[:5]):  # Limit to top 5 sources
            # Calculate similarity score (simplified)
            chunk_words = set(chunk.content.lower().split())
            query_words = set(query.lower().split())
            similarity = len(chunk_words.intersection(query_words)) / len(query_words) if query_words else 0.5
            
            source = SourceReference(
                document_id=chunk.document_id,
                chunk_id=chunk.id,
                chunk_text=chunk.content[:200] + "..." if len(chunk.content) > 200 else chunk.content,
                page_number=chunk.metadata.page_number,
                section=chunk.metadata.section_title,
                similarity_score=min(similarity * 2, 1.0)  # Boost similarity score
            )
            sources.append(source)
        
        return sources
    
    def _generate_reasoning(self, chunks: List[DocumentChunk], 
                          query: Query, answer: str) -> str:
        """Generate reasoning explanation"""
        try:
            reasoning_parts = []
            
            # Explain source selection
            if chunks:
                reasoning_parts.append(f"Based on analysis of {len(chunks)} relevant document sections")
                
                # Mention key sources
                if len(chunks) > 1:
                    reasoning_parts.append(f"The answer synthesizes information from multiple sources")
                else:
                    reasoning_parts.append(f"The answer is based on a single highly relevant source")
            
            # Explain confidence
            if len(answer) > 100:
                reasoning_parts.append("The detailed response indicates high confidence in the information")
            elif "don't know" in answer.lower() or "not enough information" in answer.lower():
                reasoning_parts.append("Limited information available in the provided context")
            
            # Explain query complexity handling
            if query.complexity and query.complexity.value == "complex":
                reasoning_parts.append("Complex query required synthesis of multiple information sources")
            
            return ". ".join(reasoning_parts) + "."
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {str(e)}")
            return "Answer generated based on available context information."


class SmartQueryProcessor(QueryProcessor):
    """Enhanced query processor with advanced optimization"""
    
    def __init__(self, vector_store: OptimizedVectorStore = None):
        super().__init__(vector_store)
        self.query_cache = {}
        
    async def process_batch_queries(self, queries: List[Query], 
                                  document_chunks: List[DocumentChunk] = None) -> List[AnswerWithExplanation]:
        """
        Process multiple queries efficiently
        
        Args:
            queries: List of queries to process
            document_chunks: Optional chunks to search in
            
        Returns:
            List of answers with explanations
        """
        try:
            logger.info(f"Processing batch of {len(queries)} queries")
            start_time = time.time()
            
            # Process queries concurrently
            tasks = [
                self.process_query(query, document_chunks)
                for query in queries
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            final_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing query {i}: {str(result)}")
                    # Create error response
                    error_answer = AnswerWithExplanation(
                        question=queries[i].text,
                        answer="Sorry, I encountered an error processing this question.",
                        processing_time_ms=0,
                        confidence=ConfidenceScore(overall=0, semantic_similarity=0, keyword_match=0, context_relevance=0),
                        sources=[],
                        reasoning="Error occurred during processing",
                        chunks_processed=0,
                        total_chunks_available=0
                    )
                    final_results.append(error_answer)
                else:
                    final_results.append(result)
            
            processing_time = (time.time() - start_time) * 1000
            logger.info(f"Batch processing completed in {processing_time:.2f}ms")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Error in batch query processing: {str(e)}")
            raise
