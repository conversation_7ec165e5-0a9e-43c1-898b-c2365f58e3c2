#!/usr/bin/env python3
"""
Final RAG Model - System Tests
Basic tests to verify system functionality
"""
import asyncio
import sys
import os
from pathlib import Path
import tempfile
import json

# Add the final directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import get_settings, validate_environment
from models.schemas import RAGRequest, ChunkSelectionStrategy
from services.rag_engine import RAGEngine
from utils.logger import get_logger, setup_logging

# Setup logging for tests
setup_logging(log_level="INFO")
logger = get_logger(__name__)


class SystemTests:
    """System test suite for Final RAG Model"""
    
    def __init__(self):
        self.engine = None
        self.test_results = []
    
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = {
            "test": test_name,
            "status": status,
            "message": message
        }
        self.test_results.append(result)
        
        symbol = "✓" if passed else "✗"
        print(f"{symbol} {test_name}: {status}")
        if message:
            print(f"  {message}")
    
    async def test_environment(self):
        """Test environment setup"""
        try:
            validate_environment()
            self.log_test_result("Environment Validation", True, "All required environment variables present")
        except Exception as e:
            self.log_test_result("Environment Validation", False, str(e))
    
    async def test_imports(self):
        """Test all imports"""
        try:
            # Test core imports
            from services.rag_engine import RAGEngine
            from services.document_processor import SmartDocumentProcessor
            from services.embedding_service import OptimizedEmbeddingService
            from services.vector_store import OptimizedVectorStore
            from services.query_processor import SmartQueryProcessor
            from services.chunk_optimizer import SmartChunkSelector
            
            # Test model imports
            from models.schemas import RAGRequest, RAGResponse
            from models.document import Document, DocumentChunk
            from models.query import Query, SearchResult
            from models.response import AnswerGeneration
            
            # Test utility imports
            from utils.logger import get_logger
            from utils.performance import PerformanceMonitor
            from utils.text_utils import TextProcessor
            from utils.validation import RequestValidator
            
            self.log_test_result("Import Test", True, "All modules imported successfully")
        except Exception as e:
            self.log_test_result("Import Test", False, str(e))
    
    async def test_configuration(self):
        """Test configuration loading"""
        try:
            settings = get_settings()
            
            # Check required settings
            required_attrs = [
                'gemini_api_key', 'gemini_model', 'chunk_size', 
                'max_chunks_per_query', 'similarity_threshold'
            ]
            
            for attr in required_attrs:
                if not hasattr(settings, attr):
                    raise ValueError(f"Missing configuration: {attr}")
            
            # Check reasonable values
            if settings.chunk_size < 100 or settings.chunk_size > 2000:
                raise ValueError(f"Invalid chunk_size: {settings.chunk_size}")
            
            if settings.max_chunks_per_query < 1 or settings.max_chunks_per_query > 50:
                raise ValueError(f"Invalid max_chunks_per_query: {settings.max_chunks_per_query}")
            
            self.log_test_result("Configuration Test", True, "Configuration loaded and validated")
        except Exception as e:
            self.log_test_result("Configuration Test", False, str(e))
    
    async def test_engine_initialization(self):
        """Test RAG engine initialization"""
        try:
            self.engine = RAGEngine()
            await self.engine.initialize()
            
            if not self.engine.is_initialized:
                raise ValueError("Engine not properly initialized")
            
            self.log_test_result("Engine Initialization", True, "RAG engine initialized successfully")
        except Exception as e:
            self.log_test_result("Engine Initialization", False, str(e))
    
    async def test_text_processing(self):
        """Test text processing utilities"""
        try:
            from utils.text_utils import clean_text, extract_keywords, calculate_similarity
            
            # Test text cleaning
            dirty_text = "  This is a test   with extra spaces.  "
            clean = clean_text(dirty_text)
            if clean != "This is a test with extra spaces.":
                raise ValueError("Text cleaning failed")
            
            # Test keyword extraction
            text = "This is a sample document about machine learning and artificial intelligence."
            keywords = extract_keywords(text, max_keywords=5)
            if not keywords or len(keywords) == 0:
                raise ValueError("Keyword extraction failed")
            
            # Test similarity calculation
            text1 = "Machine learning is a subset of artificial intelligence"
            text2 = "AI includes machine learning as a component"
            similarity = calculate_similarity(text1, text2)
            if similarity < 0 or similarity > 1:
                raise ValueError("Similarity calculation out of range")
            
            self.log_test_result("Text Processing", True, f"Keywords: {len(keywords)}, Similarity: {similarity:.2f}")
        except Exception as e:
            self.log_test_result("Text Processing", False, str(e))
    
    async def test_document_processing(self):
        """Test document processing with sample text"""
        try:
            from services.document_processor import SmartDocumentProcessor
            
            processor = SmartDocumentProcessor()
            
            # Create a sample text document
            sample_text = """
            This is a sample document for testing purposes.
            It contains multiple paragraphs and sentences.
            
            The document discusses various topics including:
            - Text processing
            - Document analysis
            - Information retrieval
            
            This should be sufficient for testing the document processing capabilities.
            The system should be able to chunk this text appropriately.
            """
            
            # Process as text document
            document = await processor.process_document(sample_text.encode(), 'txt')
            
            if not document or not document.content:
                raise ValueError("Document processing failed")
            
            # Test chunking
            chunks = await processor.create_smart_chunks(document)
            
            if not chunks or len(chunks) == 0:
                raise ValueError("Chunk creation failed")
            
            self.log_test_result("Document Processing", True, 
                               f"Document processed, {len(chunks)} chunks created")
        except Exception as e:
            self.log_test_result("Document Processing", False, str(e))
    
    async def test_embedding_service(self):
        """Test embedding service"""
        try:
            if not self.engine:
                raise ValueError("Engine not initialized")
            
            embedding_service = self.engine.embedding_service
            
            # Test single text embedding
            test_text = "This is a test sentence for embedding generation."
            embeddings = await embedding_service.encode([test_text])
            
            if embeddings is None or len(embeddings) == 0:
                raise ValueError("Embedding generation failed")
            
            if len(embeddings[0]) != embedding_service.get_dimension():
                raise ValueError("Embedding dimension mismatch")
            
            # Test batch embedding
            test_texts = [
                "First test sentence.",
                "Second test sentence.",
                "Third test sentence."
            ]
            batch_embeddings = await embedding_service.encode_batch(test_texts)
            
            if len(batch_embeddings) != len(test_texts):
                raise ValueError("Batch embedding failed")
            
            self.log_test_result("Embedding Service", True, 
                               f"Embeddings generated, dimension: {embedding_service.get_dimension()}")
        except Exception as e:
            self.log_test_result("Embedding Service", False, str(e))
    
    async def test_vector_store(self):
        """Test vector store operations"""
        try:
            if not self.engine:
                raise ValueError("Engine not initialized")
            
            from models.document import DocumentChunk, ChunkMetadata
            
            # Create test chunks
            test_chunks = []
            for i in range(3):
                chunk = DocumentChunk(
                    id=f"test_chunk_{i}",
                    document_id="test_doc",
                    content=f"This is test chunk number {i} with some content for testing.",
                    metadata=ChunkMetadata(
                        chunk_index=i,
                        start_char=i * 50,
                        end_char=(i + 1) * 50,
                        word_count=10,
                        sentence_count=1,
                        avg_sentence_length=10.0
                    )
                )
                test_chunks.append(chunk)
            
            # Add chunks to vector store
            vector_store = self.engine.vector_store
            await vector_store.add_chunks(test_chunks)
            
            # Test search
            search_results = await vector_store.search("test content", top_k=2)
            
            if not search_results or len(search_results) == 0:
                raise ValueError("Vector search failed")
            
            self.log_test_result("Vector Store", True, 
                               f"Added {len(test_chunks)} chunks, found {len(search_results)} results")
        except Exception as e:
            self.log_test_result("Vector Store", False, str(e))
    
    async def test_query_processing(self):
        """Test query processing"""
        try:
            if not self.engine:
                raise ValueError("Engine not initialized")
            
            from models.query import Query
            
            # Create test query
            query = Query(
                id="test_query",
                text="What is the content about?",
                processed_text="what is the content about"
            )
            
            # Analyze query
            query.extract_keywords()
            query.analyze_complexity()
            query.detect_query_type()
            
            if not query.keywords:
                raise ValueError("Keyword extraction failed")
            
            if not query.complexity:
                raise ValueError("Complexity analysis failed")
            
            if not query.query_type:
                raise ValueError("Query type detection failed")
            
            self.log_test_result("Query Processing", True, 
                               f"Keywords: {len(query.keywords)}, Type: {query.query_type}, Complexity: {query.complexity}")
        except Exception as e:
            self.log_test_result("Query Processing", False, str(e))
    
    async def test_request_validation(self):
        """Test request validation"""
        try:
            from utils.validation import validate_request
            
            # Test valid request
            valid_request = RAGRequest(
                documents="https://example.com/test.pdf",
                questions=["What is this about?", "What are the key points?"],
                chunk_strategy=ChunkSelectionStrategy.SMART,
                max_chunks=5,
                similarity_threshold=0.7
            )
            
            result = validate_request(valid_request)
            if not result.get("valid"):
                raise ValueError("Valid request failed validation")
            
            # Test invalid request
            try:
                invalid_request = RAGRequest(
                    documents="",  # Empty document
                    questions=[],  # Empty questions
                    max_chunks=-1,  # Invalid max_chunks
                    similarity_threshold=1.5  # Invalid threshold
                )
                validate_request(invalid_request)
                raise ValueError("Invalid request passed validation")
            except Exception:
                pass  # Expected to fail
            
            self.log_test_result("Request Validation", True, "Validation working correctly")
        except Exception as e:
            self.log_test_result("Request Validation", False, str(e))
    
    async def run_all_tests(self):
        """Run all system tests"""
        print("🧪 Final RAG Model - System Tests")
        print("=" * 50)
        
        tests = [
            self.test_environment,
            self.test_imports,
            self.test_configuration,
            self.test_engine_initialization,
            self.test_text_processing,
            self.test_document_processing,
            self.test_embedding_service,
            self.test_vector_store,
            self.test_query_processing,
            self.test_request_validation
        ]
        
        for test in tests:
            try:
                await test()
            except Exception as e:
                self.log_test_result(test.__name__, False, f"Unexpected error: {str(e)}")
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result["status"] == "PASS")
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All tests passed! System is ready to use.")
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
            
            # Show failed tests
            failed_tests = [r for r in self.test_results if r["status"] == "FAIL"]
            if failed_tests:
                print("\nFailed Tests:")
                for test in failed_tests:
                    print(f"  - {test['test']}: {test['message']}")
        
        return passed == total


async def main():
    """Main test function"""
    tests = SystemTests()
    success = await tests.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
